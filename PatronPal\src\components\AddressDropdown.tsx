/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { IoLocationOutline } from "react-icons/io5";
import {  FiPlus } from "react-icons/fi";
import countriesData from '../utils/countries.min.json';

interface AddressData {
  Line1: string;
  Line2: string;
  City: string;
  State: string;
  PostalCode: string;
  Country: string;
}

interface AddressDropdownProps {
  value: string;
  onSelectAddress: (address: AddressData) => void;
  onInputChange?: (value: string) => void;
  autoFocus?: boolean; // Add this prop to control autofocus
}


const AddressDropdown: React.FC<AddressDropdownProps> = ({ 
  value, 
  onSelectAddress, 
  onInputChange,
  autoFocus = false // Default to false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [filteredLocations, setFilteredLocations] = useState<any[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Add this effect to update the component when the value prop changes
  useEffect(() => {
    if (value) {
      setSearchTerm('');
      setIsSearching(false);
    }
  }, [value]);

  // Parse countries data - fixed to match the actual JSON structure
  const countries = useMemo(() => {
    try {
      // For debugging
      console.log("Countries data structure:", Object.keys(countriesData).length);

      // Parse the JSON structure where keys are country names and values are city arrays
      return Object.entries(countriesData).map(([countryName, cities]: [string, any]) => {
        return {
          name: countryName,
          cities: Array.isArray(cities) ? cities : []
        };
      });
    } catch (error) {
      console.error("Error parsing countries data:", error);
      return [];
    }
  }, []);

  // For debugging - log the first few countries
  // useEffect(() => {
  //   console.log("First 3 countries:", countries.slice(0, 3));
  // }, [countries]);

  // Handle input focus
  const handleInputFocus = () => {
    setIsSearching(true);
    setSearchTerm(''); // Clear search term when focusing
    setIsOpen(true);
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    setIsOpen(true);
    if (onInputChange) {
      onInputChange(newValue);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setIsSearching(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter locations based on search term
  useEffect(() => {
    // console.log("Search term:", searchTerm);

    if (!searchTerm.trim()) {
      // Show all countries when no search term
      const allCountries = countries.map(country => ({
        type: 'country',
        name: country.name
      }));

      console.log("All countries:", allCountries.length);
      setFilteredLocations(allCountries);
      return;
    }

    const term = searchTerm.toLowerCase();
    const results: any[] = [];

    // Search countries and their cities
    countries.forEach(country => {
      try {
        const countryMatches = country.name.toLowerCase().includes(term);

        // If country matches, add the country and all its cities
        if (countryMatches) {
          results.push({
            type: 'country',
            name: country.name
          });

          // Add all cities of the matching country
          country.cities.forEach(city => {
            results.push({
              type: 'city',
              name: city,
              country: country.name
            });
          });
        } else {
          // If country doesn't match, search through its cities
          country.cities.forEach(city => {
            if (city.toLowerCase().includes(term)) {
              results.push({
                type: 'city',
                name: city,
                country: country.name
              });
            }
          });
        }
      } catch (error) {
        console.error("Error processing country:", country, error);
      }
    });

    console.log("Search results:", results.length);
    setFilteredLocations(results.slice(0, 50)); // Increased limit to show more results
  }, [searchTerm, countries]);

  // Check if the search term exactly matches any existing location
  const hasExactMatch = useMemo(() => {
    if (!searchTerm.trim()) return true;

    const term = searchTerm.toLowerCase().trim();

    // Check for exact country match
    const countryMatch = countries.some(country =>
      country.name.toLowerCase() === term
    );

    // Check for exact city match
    const cityMatch = countries.some(country =>
      country.cities.some(city => city.toLowerCase() === term)
    );

    return countryMatch || cityMatch;
  }, [searchTerm, countries]);

  // Function to determine if search term looks like a country or city
  const guessLocationType = (term: string) => {
    // Simple heuristics - you can make this more sophisticated
    const upperTerm = term.toUpperCase();

    // Common country codes or patterns
    if (upperTerm.length === 2 || upperTerm.length === 3) {
      return 'country'; // Likely a country code like USA, UK, etc.
    }

    // If it contains common country indicators
    const countryIndicators = ['UNITED', 'REPUBLIC', 'KINGDOM', 'STATES'];
    if (countryIndicators.some(indicator => upperTerm.includes(indicator))) {
      return 'country';
    }

    // Default to city for most cases
    return 'city';
  };

  const handleSelectLocation = (location: any) => {
    let addressData: AddressData;

    if (location.type === 'country') {
      addressData = {
        Line1: '',
        Line2: '',
        City: '',
        State: '',
        PostalCode: '',
        Country: location.name
      };
    } else {
      // City
      addressData = {
        Line1: '',
        Line2: '',
        City: location.name,
        State: '',
        PostalCode: '',
        Country: location.country || ''
      };
    }

    onSelectAddress(addressData);
    setIsOpen(false);
    setSearchTerm('');
    setIsSearching(false);
  };

  const handleAddManualLocation = () => {
    if (!searchTerm.trim()) return;

    const locationType = guessLocationType(searchTerm.trim());

    let addressData: AddressData;

    if (locationType === 'country') {
      addressData = {
        Line1: '',
        Line2: '',
        City: '',
        State: '',
        PostalCode: '',
        Country: searchTerm.trim()
      };
    } else {
      // Treat as city
      addressData = {
        Line1: '',
        Line2: '',
        City: searchTerm.trim(),
        State: '',
        PostalCode: '',
        Country: ''
      };
    }

    onSelectAddress(addressData);
    setIsOpen(false);
    setSearchTerm('');
    setIsSearching(false);
  };

  return (
    <div className="relative w-full rounded-xl p-1.5" ref={dropdownRef}>
      <div className="relative flex items-center cursor-pointer">
        <IoLocationOutline className="absolute left-3 top-1.5 text-black text-xl" />
        <input
          type="text"
          placeholder="Search for a location..."
          className="pl-10 pr-4 py-1.5 rounded-md text-black w-full bg-white cursor-pointer outline-none"
          value={isSearching ? searchTerm : value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          autoFocus={autoFocus} // Use the prop instead of hardcoding
        />
      </div>
      {isOpen && (
        <div className="absolute z-50 mt-1 w-full bg-white rounded-md shadow-lg max-h-80 overflow-y-auto">


          <div className="p-2">
            {/* Show manual add option if no exact match found and search term exists */}
            {searchTerm.trim() && !hasExactMatch && (
              <div
                className="px-4 py-2 hover:bg-blue-50 cursor-pointer rounded-md border-b border-gray-100 mb-2"
                onClick={handleAddManualLocation}
              >
                <div className="flex items-center text-blue-600">
                  <FiPlus className="mr-2" />
                  <span className="font-medium">Add "{searchTerm.trim()}"</span>
                </div>
                <div className="text-sm text-blue-500 ml-6">
                  Add as {guessLocationType(searchTerm.trim())}
                </div>
              </div>
            )}

            {filteredLocations.length > 0 ? (
              filteredLocations.map((location, index) => (
                <div
                  key={`${location.type}-${location.name}-${index}`}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer rounded-md"
                  onClick={() => handleSelectLocation(location)}
                >
                  <div className="font-medium">{location.name}</div>
                  {location.type === 'city' && (
                    <div className="text-sm text-gray-500">{location.country}</div>
                  )}
                  {location.type === 'country' && (
                    <div className="text-sm text-gray-600">Country</div>
                  )}
                </div>
              ))
            ) : searchTerm.trim() && hasExactMatch ? (
              // This case shouldn't normally happen, but just in case
              <div className="px-4 py-6 text-center text-gray-500">
                No matching locations found in dropdown.
              </div>
            ) : !searchTerm.trim() ? null : (
              <div className="px-4 py-2 text-center text-gray-500">
                <div>No locations found in our database.</div>
                <div className="text-sm mt-1">Use the "Add" option above to add manually.</div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AddressDropdown;