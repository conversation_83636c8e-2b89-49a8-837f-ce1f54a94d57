{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-google-maps/api": "^2.20.6", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.6", "axios": "^1.9.0", "express": "^5.1.0", "formik": "^2.4.6", "google-auth-library": "^9.15.1", "lucide-react": "^0.510.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "react-toastify": "^11.0.5", "redux-persist": "^6.0.0", "tailwindcss": "^4.1.6", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/google.accounts": "^0.0.16", "@types/node": "^22.15.21", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-redux": "^7.1.34", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}