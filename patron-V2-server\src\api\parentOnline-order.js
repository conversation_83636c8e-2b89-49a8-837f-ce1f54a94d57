import parentOnlineOrder from '../models/parentOnline-order.js'
import OnlineOrderItem from '../models/subOnline-OrderItem.js'
import PatronPalCustomer from '../models/patronpalCustomers.js'

// export const GetparentOnlineOrder = async (req, res) => {
//     const onlineOrderData = await parentOnlineOrder.find().populate('subOnlineOrderId').populate('userId').populate('customerId');
//     res.send(onlineOrderData)
// }
// export const GetparentOnlineOrderById = async (req, res) => {
//     const parentOnlineOrders = await parentOnlineOrder.find(req.params).populate('subOnlineOrderId').populate('userId').populate('customerId');
//     res.send(parentOnlineOrders)
// }


export const GetparentOnlineOrder = async (req, res) => {
    try {
        const onlineOrderData = await parentOnlineOrder.find()
            .populate('userId')
            .populate('customerId');

        // Populate the subOnlineOrderId array for each parent order
        for (let order of onlineOrderData) {
            if (order.subOnlineOrderId && order.subOnlineOrderId.length > 0) {
                order.subOnlineOrderId = await OnlineOrderItem.find({ _id: { $in: order.subOnlineOrderId } })
                    .populate('customerId')
                    .populate('userId');
            }
        }

        res.send(onlineOrderData);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

    // export const GetparentOnlineOrderById = async (req, res) => {
    //     try {
    //         const { id } = req.params;
    //         const ParentOnlineOrder = await parentOnlineOrder.findById(id)
    //             .populate('userId')
    //             .populate('customerId');

    //             console.log("ParentOnlineOrder :    ",ParentOnlineOrder);
                
    //         // Populate the subOnlineOrderId array for the parent order
    //         if (ParentOnlineOrder.subOnlineOrderId && ParentOnlineOrder.subOnlineOrderId.length > 0) {
    //             ParentOnlineOrder.subOnlineOrderId = await OnlineOrderItem.find({ _id: { $in: ParentOnlineOrder.subOnlineOrderId } })
    //                 .populate('customerId')
    //                 .populate('userId');
    //         }

    //         res.send(ParentOnlineOrder);
    //     } catch (error) {
    //         res.status(500).json({ message: error.message });
    //     }
    // };



export const GetparentOnlineOrderById = async (req, res) => {
    try {
        const { customerId } = req.params;

        // Find all orders with the given customerId
        const orders = await parentOnlineOrder.find({ customerId })
            .populate('customerId')
            .populate('subOnlineOrderId');

        // Populate the subOnlineOrderId array for each parent order
        const populatedOrders = await Promise.all(orders.map(async order => {
            if (order.subOnlineOrderId && order.subOnlineOrderId.length > 0) {
                order.subOnlineOrderId = await OnlineOrderItem.find({ _id: { $in: order.subOnlineOrderId } })
                    .populate('customerId')
                    .populate('userId');
            }
            return order;
        }));

        res.send(populatedOrders);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};


// export const PostparentOnlineOrder = async (req, res) => {
//     const { customerId, subOnlineOrderId, totalAmount } = req.body;
//     const lastOrder = await parentOnlineOrder.findOne({ customerId }, {}, { sort: { '_id': -1 } });
//     const lastOrderCount = lastOrder ? (lastOrder.OrderNo || 0) : 0;
//     let numericCount
//     if (lastOrderCount != 0) {

//         numericCount = parseInt(lastOrderCount.slice(2), 10) + 1;
//     } else {

//         numericCount = Number("00001")
//     }
//     const OrderNo = `OR${numericCount.toString().padStart(4, '0')}`
//     const data = new parentOnlineOrder({ OrderNo, customerId, subOnlineOrderId,totalAmount})

//     await data.save().then(async (result) => {
//         res.json({
//             OrderNo: result.OrderNo,
//             subOnlineOrderId:result.subOnlineOrderId,
//             customerId: result.customerId,
//             totalAmount: result.totalAmount
//         })
//     })
// }


export const PostparentOnlineOrder = async (req, res) => {
    try {
        const { customerId, subOnlineOrderId, totalAmount } = req.body;
        const lastOrder = await parentOnlineOrder.findOne({ customerId }, {}, { sort: { '_id': -1 } });
        const lastOrderCount = lastOrder ? (lastOrder.OrderNo || 0) : 0;
        let numericCount;
        
        if (lastOrderCount !== 0) {
            numericCount = parseInt(lastOrderCount.slice(2), 10) + 1;
        } else {
            numericCount = Number("00001");
        }

        const OrderNo = `OR${numericCount.toString().padStart(4, '0')}`;
        const data = new parentOnlineOrder({ OrderNo, customerId, subOnlineOrderId, totalAmount });

        await data.save().then(async (result) => {
            const onlineOrderItems = await OnlineOrderItem.find({ _id: { $in: subOnlineOrderId } })
                .populate({path: 'customerId',model:'PatronPalCustomer'})
                .populate('userId');

            const populatedParentOrder = await parentOnlineOrder.findById(result._id)
                .populate();
                if (populatedParentOrder.customerId) {
                    console.log("populatedParentOrder.customerId : ", populatedParentOrder.customerId);
                
                    const customerdata = await PatronPalCustomer.findByIdAndUpdate(
                        populatedParentOrder.customerId,
                        {
                            $inc: { "CustomerLoyalty.Points": 5 }
                        },
                        { new: true }  // This option returns the updated document
                    );
                
                    console.log("customerdata : ", customerdata);
                }
            res.json({
                OrderNo: populatedParentOrder.OrderNo,
                subOnlineOrderId: populatedParentOrder.subOnlineOrderId,
                customerId: populatedParentOrder.customerId,
                totalAmount: populatedParentOrder.totalAmount,
                onlineOrderItems: onlineOrderItems // Include the fetched items in the response
            });
        });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};



export const updateparentOnlineOrder = async (req, res) => {
    try {
        const updatedData = await parentOnlineOrder.findByIdAndUpdate(
            { _id: req.params._id },
            { $set: req.body },
            { new: true }
        );

        if (updatedData) {
            res.send({ message: "parent-Online Orderitem data updated successfully", updatedData });
        } else {
            res.send({ message: "parent-Online Orderitem data cannot be updated successfully", updatedData });
        }
    } catch (error) {
        console.error(error);
        res.status(500).send({ message: "Internal server error" });
    }
}
export const deleteparentOnlineOrder = async (req, res) => {
    try {
        const deletedData = await parentOnlineOrder.findByIdAndDelete(
            { _id: req.params._id },
            { $set: req.body },
            { new: true }
        );

        if (updatedData) {
            res.send({ message: "parent-Online Orderitem data deleted successfully", deletedData });
        } else {
            res.send({ message: "parent-Online Orderitem data cannot be deleted successfully", deletedData });
        }
    } catch (error) {
        console.error(error);
        res.status(500).send({ message: "Internal server error" });
    }
}