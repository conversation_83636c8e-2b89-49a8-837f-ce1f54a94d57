import express  from "express";
const routes=express.Router();

import {Checkout,
    createAppSubscription,
    createHardwareSubscription,
    getCustomerSubscriptionsByEmail,
    getSubscriptions,
     cancelSubscription,
    getCharges,
    getallamount,
    SurCharge,
    PaymentIntent,
    GetChargesByAccountId,
    createInvoice,
    getCustomers,
    getAppProducts,
    updateSubscription
} from "../api/checkout.js"
import { createSellerAccount,
    authorizeSeller,
     getSellerBalance,
     DeleteConnectAccount,
     getCompletedAccounts,
     getAllChargesWithAccountId
} from "../api/stripeConnet.js"
import {
    orderPaymentIntent,
    terminalConnection,
    capturePaymentIntent
    ,
    confirmPaymentIntent
} from '../api/stripe-terminal.js'

routes.get('/charges/:_accountid',GetChargesByAccountId)
routes.get('/sellerbalance/:account_id',getSellerBalance)
routes.get('/sellerAccounts',getCompletedAccounts)
routes.get('/stripe-transactions',getAllChargesWithAccountId)
routes.get('/stripe-customers',getCustomers)
routes.get('/subscriptions/:email',getCustomerSubscriptionsByEmail)
routes.get('/subscriptions',getSubscriptions)
routes.get('/stripe-products',getAppProducts)
routes.post('/cancel-subscription/:subscriptionId',cancelSubscription)
routes.post('/create-invoice',createInvoice)
routes.post('/update-plan',updateSubscription)


routes.get('/charges',getCharges)
routes.get('/allamount',getallamount)
routes.post('/authorize-seller/:userId', authorizeSeller )
routes.post('/credit', Checkout )
routes.post('/surCharge', SurCharge )
routes.post('/app-plan',createAppSubscription  )
routes.post('/hardware-plan',createHardwareSubscription  )
routes.post('/create-account', createSellerAccount )
routes.post('/connect-terminal', terminalConnection )
routes.delete('/delete-Account/:accountId',DeleteConnectAccount)
routes.post('/order-payment', orderPaymentIntent )
routes.post('/paymentIntent', PaymentIntent )
routes.post('/capture_payment', capturePaymentIntent )
routes.post('/confirm_payment', confirmPaymentIntent )

export default routes