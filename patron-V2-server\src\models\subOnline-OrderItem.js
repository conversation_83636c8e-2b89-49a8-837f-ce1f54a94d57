import mongoose from 'mongoose';

const subOrderItemSchema = new mongoose.Schema({
    orderNo: {
        type: Number,
    },
    OrderNumber: {
        type: String,
    },
    Status: {
        type: String,
        default: 'patronpal order',
    },
    orderType: {
        type: String,
        enum: ['delivery', 'pickup']
    },
    deliveryOptions: {
        dropOffAddress: {
            type: String
        },
        dropOffInstructions: {
            type: String
        },
        expressDelivery: {
            type: String
        }
    },
    pickUpObj: {
        pickupAddress: {
            type: String
        },
        pickupOptions: {
            standardPickup: {
                type: Boolean
            },
            schedulePickup: {
                type: Boolean
            },
            startTime: String,
            endTime: String,
            Date: String
        }
    },
    orderStatus: {
        type: String,
        enum: ['new order', 'preparing', 'ready','done'],
    },
    tax: [{
        name: {
            type: String,
        },
        addtax: {
            type: Number,
        },
    }],
    product: {
        type: Array,
    },
    loyalityOffer: {
        type: Array
    },
    couponOffer: {
        type: Array
    },
    customerId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'customer',
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'user',
    },
    PaymentStatus: {
        type: String,
        enum: ['cash', 'online'],
    },
    deliveryfee: {
        type: Number,
    },
    paymentIntentId:{
        type:String
    },
    chargeId:{
        type:String
    },
    Amount: {
        type: Number,
    },
}, { timestamps: true });

const subOrderItem = mongoose.model('OnlineOrderItem', subOrderItemSchema);

export default subOrderItem;