import React, { useEffect, useState } from 'react';
import { FcGoogle } from 'react-icons/fc';

const GoogleAuthTest: React.FC = () => {
  const [isGoogleLoaded, setIsGoogleLoaded] = useState(false);
  const [loading, setLoading] = useState(false);

  const GOOGLE_CLIENT_ID = "************-mc939due2sphq0lgslraliga9h36j8gf.apps.googleusercontent.com";

  useEffect(() => {
    // Check if Google API is loaded
    const checkGoogleAPI = () => {
      if (window.google?.accounts?.id) {
        setIsGoogleLoaded(true);
        console.log('✅ Google API is loaded');
      } else {
        console.log('❌ Google API not loaded');
        loadGoogleAPI();
      }
    };

    checkGoogleAPI();
  }, []);

  const loadGoogleAPI = async () => {
    try {
      // Check if script already exists
      const existingScript = document.querySelector('script[src*="accounts.google.com/gsi/client"]');
      if (existingScript) {
        console.log('Google script already exists, waiting for load...');
        let attempts = 0;
        const checkLoaded = () => {
          if (window.google?.accounts?.id || attempts > 50) {
            setIsGoogleLoaded(!!window.google?.accounts?.id);
          } else {
            attempts++;
            setTimeout(checkLoaded, 100);
          }
        };
        checkLoaded();
        return;
      }

      console.log('Loading Google API...');
      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      
      script.onload = () => {
        console.log('✅ Google API loaded successfully');
        setIsGoogleLoaded(true);
      };
      
      script.onerror = () => {
        console.error('❌ Failed to load Google API');
        setIsGoogleLoaded(false);
      };
      
      document.head.appendChild(script);
    } catch (error) {
      console.error('Error loading Google API:', error);
    }
  };

  const handleGoogleResponse = (response: any) => {
    console.log('Google response received:', response);
    setLoading(false);
    
    if (response.credential) {
      try {
        // Decode the JWT token
        const base64Url = response.credential.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(
          atob(base64)
            .split('')
            .map(function(c) {
              return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            })
            .join('')
        );
        const userData = JSON.parse(jsonPayload);
        
        console.log('✅ User data:', userData);
        alert(`Success! Signed in as: ${userData.email}\nName: ${userData.name}`);
      } catch (error) {
        console.error('Error decoding token:', error);
      }
    }
  };

  const testGoogleSignIn = () => {
    if (!isGoogleLoaded) {
      alert('Google API not loaded yet. Please wait and try again.');
      return;
    }

    setLoading(true);
    console.log('🔄 Testing Google Sign-In...');

    try {
      // Initialize Google
      window.google.accounts.id.initialize({
        client_id: GOOGLE_CLIENT_ID,
        callback: handleGoogleResponse,
        auto_select: false,
        cancel_on_tap_outside: false,
        ux_mode: 'popup'
      });

      // Disable auto-select to force account chooser
      window.google.accounts.id.disableAutoSelect();

      // Try to show the prompt
      window.google.accounts.id.prompt((notification: any) => {
        console.log('Google prompt result:', notification);
        
        if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
          console.log('Native prompt failed, showing manual button...');
          showManualButton();
        } else {
          console.log('Google prompt displayed successfully');
          setLoading(false);
        }
      });
    } catch (error) {
      console.error('Error testing Google Sign-In:', error);
      setLoading(false);
    }
  };

  const showManualButton = () => {
    // Create a visible container for Google button
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'fixed';
    tempContainer.style.top = '50%';
    tempContainer.style.left = '50%';
    tempContainer.style.transform = 'translate(-50%, -50%)';
    tempContainer.style.zIndex = '10000';
    tempContainer.style.backgroundColor = 'white';
    tempContainer.style.padding = '20px';
    tempContainer.style.borderRadius = '8px';
    tempContainer.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';
    document.body.appendChild(tempContainer);

    // Add a message
    const message = document.createElement('div');
    message.textContent = 'Click the Google button below to sign in:';
    message.style.marginBottom = '10px';
    message.style.textAlign = 'center';
    tempContainer.appendChild(message);

    // Render Google button
    const buttonContainer = document.createElement('div');
    tempContainer.appendChild(buttonContainer);

    window.google.accounts.id.renderButton(buttonContainer, {
      theme: 'outline',
      size: 'large',
      type: 'standard',
      shape: 'rectangular',
      text: 'signin_with',
      logo_alignment: 'left',
      width: 300
    });

    // Add close button
    const closeButton = document.createElement('button');
    closeButton.textContent = 'Cancel';
    closeButton.style.marginTop = '10px';
    closeButton.style.width = '100%';
    closeButton.style.padding = '8px';
    closeButton.style.border = '1px solid #ccc';
    closeButton.style.borderRadius = '4px';
    closeButton.style.backgroundColor = '#f5f5f5';
    closeButton.style.cursor = 'pointer';
    closeButton.onclick = () => {
      if (document.body.contains(tempContainer)) {
        document.body.removeChild(tempContainer);
      }
      setLoading(false);
    };
    tempContainer.appendChild(closeButton);

    // Auto-remove after 30 seconds
    setTimeout(() => {
      if (document.body.contains(tempContainer)) {
        document.body.removeChild(tempContainer);
        setLoading(false);
      }
    }, 30000);
  };

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Google Auth Test</h2>
      
      <div className="mb-4">
        <p className="text-sm text-gray-600 mb-2">
          Google API Status: 
          <span className={`ml-2 font-medium ${isGoogleLoaded ? 'text-green-600' : 'text-red-600'}`}>
            {isGoogleLoaded ? '✅ Loaded' : '❌ Not Loaded'}
          </span>
        </p>
      </div>

      <button
        onClick={testGoogleSignIn}
        disabled={!isGoogleLoaded || loading}
        className="flex items-center justify-center w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        {loading ? (
          <>
            <div className="animate-spin w-5 h-5 mr-3 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            Testing...
          </>
        ) : (
          <>
            <FcGoogle className="w-5 h-5 mr-3" />
            Test Google Sign-In
          </>
        )}
      </button>

      <div className="mt-4 text-xs text-gray-500">
        <p>This will test the Google authentication flow.</p>
        <p>Check the browser console for detailed logs.</p>
      </div>
    </div>
  );
};

export default GoogleAuthTest;
