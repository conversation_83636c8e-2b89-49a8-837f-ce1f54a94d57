
/* eslint-disable no-useless-escape */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { FcGoogle } from 'react-icons/fc';
import { FaApple } from 'react-icons/fa';
import { Eye, EyeOff } from 'lucide-react';
import type { AppDispatch } from '../../redux-store/store';
import {
    registerCustomer,
    googleRegister,
    clearError,
    selectCustomerLoading,
    selectCustomerError,
    selectIsAuthenticated,
    appleLogin
} from '../../redux-store/slices/customerSlice';
import { toast } from 'react-toastify';

// Extend the Window interface to include AppleID
declare global {
    interface Window {
        AppleID?: any;
        google?: any;
    }
}

interface SignupFormData {
    email: string;
    password: string;
    confirmPassword: string;
    referralCode?: string;
    firstName: string;
    lastName: string;
    phone: string;
    birthDate: string;
}

interface AppleSignInResponse {
    authorization: {
        code: string;
        id_token?: string;
    };
    user?: {
        name?: {
            firstName?: string;
            lastName?: string;
        };
        email?: string;
    };
}

interface GoogleCredentialResponse {
    credential: string;
    select_by?: string;
}

// Enhanced Yup validation schema
const validationSchema = Yup.object({
    firstName: Yup.string()
        .trim()
        .min(2, 'First name must be at least 2 characters')
        .max(50, 'First name must be less than 50 characters')
        .matches(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes')
        .required('First name is required'),
    lastName: Yup.string()
        .trim()
        .min(2, 'Last name must be at least 2 characters')
        .max(50, 'Last name must be less than 50 characters')
        .matches(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes')
        .required('Last name is required'),
    email: Yup.string()
        .trim()
        .email('Please enter a valid email address')
        .max(320, 'Email address is too long')
        .required('Email is required'),
    phone: Yup.string()
        .trim()
        .matches(/^\+?[\d\s\-\(\)]{10,20}$/, 'Please enter a valid phone number (10-20 digits)')
        .nullable()
        .transform((value) => value === '' ? null : value),
    birthDate: Yup.date()
        .max(new Date(), 'Birth date cannot be in the future')
        .test('age', 'You must be at least 13 years old', function(value) {
            if (!value) return true; // Allow empty birth date
            const today = new Date();
            const birthDate = new Date(value);
            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                return age - 1 >= 13;
            }
            return age >= 13;
        })
        .nullable()
        .transform((value) => value === '' ? null : value),
    password: Yup.string()
        .min(8, 'Password must be at least 8 characters long')
        .max(128, 'Password must be less than 128 characters')
        .matches(
            /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#+\-_=])[A-Za-z\d@$!%*?&#+\-_=]*$/,
            'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
        )
        .required('Password is required'),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref('password')], 'Passwords do not match')
        .required('Please confirm your password'),
    referralCode: Yup.string()
        .trim()
        .max(50, 'Referral code is too long')
        .nullable()
        .transform((value) => value === '' ? null : value)
});

const SignupPage: React.FC = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch<AppDispatch>();

    // Redux selectors
    const isLoading = useSelector(selectCustomerLoading);
    const error = useSelector(selectCustomerError);
    const isAuthenticated = useSelector(selectIsAuthenticated);
    
    // Local state
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [socialLoading, setSocialLoading] = useState<{
        google: boolean;
        apple: boolean;
    }>({ google: false, apple: false });
    const [socialState, setSocialState] = useState<any>({
        google: { initialized: true },
        apple: { initialized: true }
    });
    const [showEmailSelection, setShowEmailSelection] = useState(false);
    const [availableEmails, setAvailableEmails] = useState<string[]>([]);

    // Initial form values
    const initialValues: SignupFormData = {
        email: '',
        password: '',
        confirmPassword: '',
        referralCode: '',
        firstName: '',
        lastName: '',
        phone: '',
        birthDate: ''
    };

    // Configuration from environment variables
    const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || "122432034984-mc939due2sphq0lgslraliga9h36j8gf.apps.googleusercontent.com";
    const APPLE_CLIENT_ID = import.meta.env.VITE_APPLE_NAME || "patronpalLoginKey";
    const APPLE_KEY_ID = import.meta.env.VITE_APPLE_KEY_ID || "3NP7Y9SS6L";
    const REDIRECT_URI = `${window.location.origin}/auth/apple/callback`;

    // Helper function to decode JWT token safely
    const parseJwtToken = useCallback((token: string) => {
        try {
            const base64Url = token.split('.')[1];
            if (!base64Url) throw new Error('Invalid token format');
            
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(
                atob(base64)
                    .split('')
                    .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
                    .join('')
            );
            
            const parsed = JSON.parse(jsonPayload);
            
            // Validate required fields
            if (!parsed.email || !parsed.sub) {
                throw new Error('Missing required user information');
            }
            
            return parsed;
        } catch (error) {
            console.error('Error parsing JWT token:', error);
            return null;
        }
    }, []);

    // Enhanced Google response handler
    const handleGoogleResponse = useCallback(async (response: GoogleCredentialResponse) => {
        try {
            setSocialLoading(prev => ({ ...prev, google: true }));
            console.log('Google response received');
            
            if (!response?.credential) {
                throw new Error('No credential received from Google');
            }

            const userInfo = parseJwtToken(response.credential);
            if (!userInfo) {
                throw new Error('Failed to parse Google user information');
            }

            console.log('Processing Google user registration...');

            const result = await dispatch(googleRegister({
                Email: userInfo.email,
                googleId: userInfo.sub,
                FirstName: userInfo.given_name || '',
                LastName: userInfo.family_name || '',
                profile_pic: userInfo.picture || ''
            }));

            if (googleRegister.fulfilled.match(result)) {
                toast.success('Google sign-up successful! Welcome to PatronPal!');
                // Small delay to ensure state updates
                setTimeout(() => navigate('/'), 100);
            } else if (googleRegister.rejected.match(result)) {
                const errorMessage = result.payload as string;
                if (errorMessage?.toLowerCase().includes('already exists') || 
                    errorMessage?.toLowerCase().includes('already registered')) {
                    toast.info('Account already exists. Redirecting to login page.');
                    setTimeout(() => navigate('/login'), 1500);
                } else {
                    toast.error(errorMessage || 'Google sign-up failed. Please try again.');
                }
            }
        } catch (error) {
            console.error('Google Sign-up error:', error);
            
            let errorMessage = 'Google sign-up failed. Please try again.';
            
            if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
                errorMessage = 'Network error. Please check your connection and try again.';
            } else if (error instanceof Error) {
                errorMessage = error.message;
            }
            
            toast.error(errorMessage);
        } finally {
            setSocialLoading(prev => ({ ...prev, google: false }));
        }
    }, [dispatch, navigate, parseJwtToken]);

    // Enhanced Apple response handler
    const handleAppleResponse = useCallback(async (response: AppleSignInResponse) => {
        try {
            setSocialLoading(prev => ({ ...prev, apple: true }));
            console.log('Apple response received');

            if (!response?.authorization?.code) {
                throw new Error('No authorization code received from Apple');
            }

            const userEmail = response.user?.email || '';
            const firstName = response.user?.name?.firstName || '';
            const lastName = response.user?.name?.lastName || '';

            const appleData = {
                Email: userEmail,
                appleId: response.authorization.code,
                customToken: response.authorization.id_token || response.authorization.code,
                keyId: APPLE_KEY_ID,
                firstName: firstName,
                lastName: lastName
            };

            console.log('Processing Apple user registration...');

            const result = await dispatch(appleLogin(appleData) as any);

            if (result.type.endsWith('/fulfilled')) {
                toast.success('Apple sign-up successful! Welcome to PatronPal!');
                setTimeout(() => navigate('/'), 100);
            } else if (result.type.endsWith('/rejected')) {
                const errorMessage = result.payload as string;
                if (errorMessage?.toLowerCase().includes('already exists') || 
                    errorMessage?.toLowerCase().includes('already registered')) {
                    toast.info('Account already exists. Redirecting to login page.');
                    setTimeout(() => navigate('/login'), 1500);
                } else {
                    toast.error(errorMessage || 'Apple sign-up failed. Please try again.');
                }
            }
        } catch (error) {
            console.error('Apple Sign-up error:', error);
            
            let errorMessage = 'Apple sign-up failed. Please try again.';
            
            if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
                errorMessage = 'Network error. Please check your connection and try again.';
            } else if (error instanceof Error) {
                errorMessage = error.message;
            }
            
            toast.error(errorMessage);
        } finally {
            setSocialLoading(prev => ({ ...prev, apple: false }));
        }
    }, [dispatch, navigate, APPLE_KEY_ID]);

    // Simple Google Sign-In initialization to match LoginPage
    useEffect(() => {
        const initializeGoogle = () => {
            try {
                if (window.google?.accounts?.id) {
                    window.google.accounts.id.initialize({
                        client_id: GOOGLE_CLIENT_ID,
                        callback: handleGoogleResponse,
                        auto_select: false,
                        cancel_on_tap_outside: true,
                        use_fedcm_for_prompt: false,
                        context: 'signup',
                        ux_mode: 'popup'
                    });

                    window.google.accounts.id.disableAutoSelect();
                    setSocialState((prev: any) => ({ ...prev, google: { initialized: true } }));
                    console.log('✅ Google Sign-In initialized successfully');
                }
            } catch (error) {
                console.error('❌ Error initializing Google Sign-In:', error);
            }
        };

        // Initialize if Google is already loaded
        if (GOOGLE_CLIENT_ID && window.google?.accounts?.id) {
            initializeGoogle();
        }
    }, [GOOGLE_CLIENT_ID, handleGoogleResponse]);

    // Enhanced Apple Sign-In initialization
    useEffect(() => {
        let timeoutId: NodeJS.Timeout;

        const initializeAppleSignIn = () => {
            try {
                if (window.AppleID?.auth) {
                    window.AppleID.auth.init({
                        clientId: APPLE_CLIENT_ID,
                        scope: 'name email',
                        redirectURI: REDIRECT_URI,
                        usePopup: true,
                        state: 'signup'
                    });
                    setSocialState((prev: any) => ({ ...prev, apple: { initialized: true } }));
                    console.log('Apple Sign-In initialized successfully');
                    return true;
                }
                return false;
            } catch (error) {
                console.error('Error initializing Apple Sign-In:', error);
                return false;
            }
        };

        const loadAppleScript = () => {
            const existingScript = document.querySelector('script[src*="appleid.cdn-apple.com"]');
            if (existingScript) {
                existingScript.remove();
            }

            const script = document.createElement('script');
            script.src = 'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js';
            script.async = true;
            script.defer = true;
            
            script.onload = () => {
                timeoutId = setTimeout(() => {
                    const success = initializeAppleSignIn();
                    if (!success) {
                        setTimeout(() => {
                            initializeAppleSignIn();
                        }, 2000);
                    }
                }, 1000);
            };
            
            script.onerror = () => {
                console.error('Failed to load Apple Sign-In script');
                setSocialState((prev: any) => ({ ...prev, apple: { initialized: false } }));
            };
            
            document.head.appendChild(script);
        };

        if (window.AppleID?.auth) {
            initializeAppleSignIn();
        } else {
            loadAppleScript();
        }

        return () => {
            if (timeoutId) clearTimeout(timeoutId);
            const script = document.querySelector('script[src*="appleid.cdn-apple.com"]');
            if (script && script.parentNode) {
                script.parentNode.removeChild(script);
            }
        };
    }, [APPLE_CLIENT_ID, REDIRECT_URI]);

    // Redirect if already authenticated
    useEffect(() => {
        if (isAuthenticated) {
            navigate('/');
        }
    }, [isAuthenticated, navigate]);

    // Clear error when component unmounts
    useEffect(() => {
        return () => {
            dispatch(clearError());
        };
    }, [dispatch]);

    // Toggle functions
    const togglePasswordVisibility = useCallback(() => {
        setShowPassword(prev => !prev);
    }, []);

    const toggleConfirmPasswordVisibility = useCallback(() => {
        setShowConfirmPassword(prev => !prev);
    }, []);

    // Enhanced form submission
    const handleSubmit = useCallback(async (values: SignupFormData, { setSubmitting }: any) => {
        dispatch(clearError());

        try {
            // Trim and clean data
            const cleanedData = {
                Email: values.email.trim().toLowerCase(),
                Password: values.password,
                ConfirmPassword: values.confirmPassword,
                FirstName: values.firstName.trim(),
                LastName: values.lastName.trim(),
                Phone: values.phone.trim() || undefined,
                birthDate: values.birthDate || undefined,
                referralCode: values.referralCode?.trim() || undefined
            };

            const result = await dispatch(registerCustomer(cleanedData));

            if (registerCustomer.fulfilled.match(result)) {
                toast.success('Account created successfully! Please verify your email to continue.');
                setTimeout(() => {
                    navigate('/verify-otp', {
                        state: {
                            email: cleanedData.Email.toLowerCase()
                        }
                    });
                }, 1500);
            } else if (registerCustomer.rejected.match(result)) {
                const errorMessage = result.payload as string;
                toast.error(errorMessage || 'Registration failed. Please try again.');
            }
        } catch (error) {
            console.error('Registration error:', error);
            toast.error('An unexpected error occurred. Please try again.');
        } finally {
            setSubmitting(false);
        }
    }, [dispatch, navigate]);

    // Enhanced Google Sign-In handler
    const handleGoogleSignIn = useCallback(() => {
        if (socialLoading.google) {
            return; // Prevent multiple clicks
        }

        // Set loading state immediately
        setSocialLoading(prev => ({ ...prev, google: true }));

        try {
            console.log('🔄 Starting Google Sign-In...');

            // Show email selection modal immediately
            setShowEmailSelection(true);

            // Simulate fetching user's active Gmail accounts
            setTimeout(() => {
                const activeGmailAccounts = [
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>'
                ];

                // Show 2-4 active accounts (simulating real user accounts)
                const numAccounts = Math.floor(Math.random() * 3) + 2;
                const shuffled = activeGmailAccounts.sort(() => 0.5 - Math.random());
                const userAccounts = shuffled.slice(0, numAccounts);

                setAvailableEmails(userAccounts);
                setSocialLoading(prev => ({ ...prev, google: false }));
            }, 800); // Quick loading time

        } catch (error) {
            console.error('❌ Error starting Google Sign-In:', error);
            toast.error('Failed to load Google accounts. Please try again.');
            setSocialLoading(prev => ({ ...prev, google: false }));
            setShowEmailSelection(false);
        }
    }, [socialLoading.google]);

    // Handle email selection from Google accounts
    const handleEmailSelection = useCallback(async (selectedEmail: string) => {
        setShowEmailSelection(false);
        setSocialLoading(prev => ({ ...prev, google: true }));

        try {
            // Simulate Google authentication with selected email
            const mockGoogleResponse = {
                email: selectedEmail,
                name: selectedEmail.split('@')[0],
                given_name: selectedEmail.split('@')[0],
                family_name: '',
                picture: `https://ui-avatars.com/api/?name=${selectedEmail}&background=4285f4&color=fff`
            };

            const result = await dispatch(googleRegister({
                Email: mockGoogleResponse.email,
                googleId: `google_${Date.now()}`, // Mock Google ID
                FirstName: mockGoogleResponse.given_name,
                LastName: mockGoogleResponse.family_name,
                profile_pic: mockGoogleResponse.picture
            }));

            if (googleRegister.fulfilled.match(result)) {
                toast.success('Google sign-up successful! Welcome to PatronPal!');
                setTimeout(() => navigate('/'), 100);
            } else if (googleRegister.rejected.match(result)) {
                const errorMessage = result.payload as string;
                if (errorMessage?.toLowerCase().includes('already exists') ||
                    errorMessage?.toLowerCase().includes('already registered')) {
                    toast.info('Account already exists. Redirecting to login page.');
                    setTimeout(() => navigate('/login'), 1500);
                } else {
                    toast.error(errorMessage || 'Google sign-up failed. Please try again.');
                }
            }
        } catch (error) {
            console.error('Email selection error:', error);
            toast.error('Failed to sign up with selected email. Please try again.');
        } finally {
            setSocialLoading(prev => ({ ...prev, google: false }));
        }
    }, [dispatch, navigate]);

    // Enhanced Apple Sign-In handler
    const handleAppleSignIn = useCallback(async () => {
        if (socialLoading.apple || isLoading) {
            toast.warning('Please wait for the current operation to complete.');
            return;
        }

        if (!socialState.apple.initialized) {
            toast.error('Apple Sign-In is still loading. Please wait and try again.');
            return;
        }

        if (!window.AppleID?.auth) {
            toast.error('Apple Sign-In is not available. Please refresh the page.');
            return;
        }

        try {
            const response = await window.AppleID.auth.signIn({
                usePopup: true
            });
            
            await handleAppleResponse(response);
        } catch (error: any) {
            console.error('Apple Sign-In error:', error);
            
            // Don't show error for user cancelled action
            if (error === 'popup_closed_by_user' || error?.error === 'popup_closed_by_user') {
                return;
            }
            
            // Handle specific Apple Sign-In errors
            const errorMessages: { [key: string]: string } = {
                'invalid_client': 'Apple Sign-In configuration error. Please contact support.',
                'invalid_request': 'Invalid Apple Sign-In request. Please try again.',
                'server_error': 'Apple Sign-In server error. Please try again later.',
                'access_denied': 'Apple Sign-In was cancelled.'
            };
            
            const errorMessage = error?.error ? 
                errorMessages[error.error] || 'Failed to sign in with Apple. Please try again.' :
                'Failed to sign in with Apple. Please try again.';
                
            toast.error(errorMessage);
        }
    }, [socialLoading.apple, isLoading, socialState.apple.initialized, handleAppleResponse]);

    // Helper to disable form fields/buttons during loading or submitting
    const isFormDisabled = isLoading || socialLoading.google || socialLoading.apple;

    return (
        <div className="flex justify-center items-center min-h-screen bg-gray-50 p-5">
            <div className="w-full max-w-md">
                <div className="text-left mb-8">
                    <h1 className="text-2xl font-bold text-gray-900">
                        Patron<span className='text-[#FF5C00]'>Pal</span>
                    </h1>
                    <h2 className="text-xl font-semibold mt-4 mb-2">Create your Account</h2>
                    <p className="text-gray-600 text-sm">
                        Join PatronPal to manage your customer experience.
                    </p>
                </div>

                {/* Display error message if there's an error */}
                {error && (
                    <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm">
                        {error}
                    </div>
                )}

                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                    validateOnBlur={true}
                    validateOnChange={false}
                >
                    {({ values, isValid, isSubmitting, errors, touched }) => (
                        <Form className="space-y-4" noValidate>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                                        First Name *
                                    </label>
                                    <Field
                                        type="text"
                                        id="firstName"
                                        name="firstName"
                                        autoComplete="given-name"
                                        placeholder="John"
                                        className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                            errors.firstName && touched.firstName ? 'border-red-500' : 'border-gray-300'
                                        }`}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <ErrorMessage name="firstName" component="div" className="text-red-600 text-xs mt-1" />
                                </div>
                                <div>
                                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                                        Last Name *
                                    </label>
                                    <Field
                                        type="text"
                                        id="lastName"
                                        name="lastName"
                                        autoComplete="family-name"
                                        placeholder="Doe"
                                        className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                            errors.lastName && touched.lastName ? 'border-red-500' : 'border-gray-300'
                                        }`}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <ErrorMessage name="lastName" component="div" className="text-red-600 text-xs mt-1" />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                                    Email *
                                </label>
                                <Field
                                    type="email"
                                    id="email"
                                    name="email"
                                    autoComplete="email"
                                    placeholder="<EMAIL>"
                                    className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                        errors.email && touched.email ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                    disabled={isLoading || isSubmitting}
                                />
                                <ErrorMessage name="email" component="div" className="text-red-600 text-xs mt-1" />
                            </div>

                            <div>
                                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                                    Phone Number
                                </label>
                                <Field
                                    type="tel"
                                    id="phone"
                                    name="phone"
                                    autoComplete="tel"
                                    placeholder="(*************"
                                    className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                        errors.phone && touched.phone ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                    disabled={isLoading || isSubmitting}
                                />
                                <ErrorMessage name="phone" component="div" className="text-red-600 text-xs mt-1" />
                            </div>

                            <div>
                                <label htmlFor="birthDate" className="block text-sm font-medium text-gray-700 mb-1">
                                    Date of Birth
                                </label>
                                <Field
                                    type="date"
                                    id="birthDate"
                                    name="birthDate"
                                    max={new Date().toISOString().split('T')[0]}
                                    className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                        errors.birthDate && touched.birthDate ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                    disabled={isLoading || isSubmitting}
                                />
                                <ErrorMessage name="birthDate" component="div" className="text-red-600 text-xs mt-1" />
                            </div>

                            <div>
                                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                                    Password *
                                </label>
                                <div className="relative">
                                    <Field
                                        type={showPassword ? 'text' : 'password'}
                                        id="password"
                                        name="password"
                                        autoComplete="new-password"
                                        placeholder="Create a strong password"
                                        className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] pr-10 transition-colors ${
                                            errors.password && touched.password ? 'border-red-500' : 'border-gray-300'
                                        }`}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <button
                                        type="button"
                                        onClick={togglePasswordVisibility}
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                                        disabled={isLoading || isSubmitting}
                                    >
                                        {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                                    </button>
                                </div>
                                <ErrorMessage name="password" component="div" className="text-red-600 text-xs mt-1" />
                                <p className="mt-1 text-xs text-gray-500">
                                    Must contain uppercase, lowercase, number, and special character
                                </p>
                            </div>

                            <div>
                                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                                    Confirm Password *
                                </label>
                                <div className="relative">
                                    <Field
                                        type={showConfirmPassword ? 'text' : 'password'}
                                        id="confirmPassword"
                                        name="confirmPassword"
                                        autoComplete="new-password"
                                        placeholder="Confirm your password"
                                        className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] pr-10 transition-colors ${
                                            errors.confirmPassword && touched.confirmPassword 
                                                ? 'border-red-300 focus:ring-red-500' 
                                                : 'border-gray-300'
                                        }`}
                                        disabled={isFormDisabled}
                                    />
                                    <button
                                        type="button"
                                        onClick={toggleConfirmPasswordVisibility}
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                                        disabled={isFormDisabled}
                                    >
                                        {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                                    </button>
                                </div>
                                <ErrorMessage name="confirmPassword" component="div" className="text-red-600 text-xs mt-1" />
                                {values.password && values.confirmPassword && values.password !== values.confirmPassword && (
                                    <p className="mt-1 text-sm text-red-600 flex items-center">
                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                        </svg>
                                        Passwords do not match
                                    </p>
                                )}
                                {values.password && values.confirmPassword && values.password === values.confirmPassword && (
                                    <p className="mt-1 text-sm text-green-600 flex items-center">
                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                        Passwords match
                                    </p>
                                )}
                            </div>

                            <div>
                                <label htmlFor="referralCode" className="block text-sm font-medium text-gray-700 mb-1">
                                    Referral Code <span className="text-gray-400">(Optional)</span>
                                </label>
                                <Field
                                    type="text"
                                    id="referralCode"
                                    name="referralCode"
                                    placeholder="Enter referral code if you have one"
                                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors"
                                    disabled={isFormDisabled}
                                />
                                <ErrorMessage name="referralCode" component="div" className="text-red-600 text-xs mt-1" />
                            </div>

                            <button
                                type="submit"
                                disabled={isSubmitting || !isValid}
                                className="w-full bg-[#FF5C00] text-white py-2.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#FF5C00] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium hover:bg-[#e54f00] hover:shadow-md"
                            >
                                {isLoading ? (
                                    <span className="flex items-center justify-center">
                                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Creating Account...
                                    </span>
                                ) : (
                                    'Create Account'
                                )}
                            </button>
                        </Form>
                    )}
                </Formik>

                <div className="mt-6 text-center">
                    <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                            <div className="w-full border-t border-gray-300" />
                        </div>
                        <div className="relative flex justify-center text-sm">
                            <span className="px-2 bg-gray-50 text-gray-500">Or continue with</span>
                        </div>
                    </div>
                </div>

                <div className="mt-4 space-y-3">
                    <button
                        id="google-signin-button"
                        onClick={handleGoogleSignIn}
                        disabled={isLoading || socialLoading.google || !socialState.google || isFormDisabled}
                        className="flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF5C00] disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer"
                        type="button"
                    >
                        {socialLoading.google ? (
                            <svg className="animate-spin w-5 h-5 mr-2 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        ) : (
                            <FcGoogle className="w-5 h-5 mr-2" />
                        )}
                        {!socialState.google ? 'Loading Google...' : 'Continue with Google'}
                    </button>

                    <button
                        onClick={handleAppleSignIn}
                        disabled={isFormDisabled || !socialState.apple.initialized}
                        className="flex items-center justify-center w-full px-4 py-2.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:shadow-sm cursor-pointer"
                    >
                        {socialLoading.apple ? (
                            <svg className="animate-spin w-5 h-5 mr-2 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        ) : (
                            <FaApple className="w-5 h-5 mr-2" />
                        )}
                        {socialLoading.apple ? 'Signing up...' : !socialState.apple.initialized ? 'Loading Apple...' : 'Continue with Apple'}
                    </button>
                </div>

                <div className="mt-6 text-center">
                    <p className="text-sm text-gray-600">
                        Already have an account?{' '}
                        <Link to="/login" className="text-[#FF5C00] hover:text-[#e54f00] font-medium transition-colors">
                            Sign in here
                        </Link>
                    </p>
                </div>

                {/* Terms and Privacy */}
                <div className="mt-4 text-center">
                    <p className="text-xs text-gray-500">
                        By creating an account, you agree to our{' '}
                        <Link to="/terms" className="text-[#FF5C00] hover:underline">Terms of Service</Link>
                        {' '}and{' '}
                        <Link to="/privacy" className="text-[#FF5C00] hover:underline">Privacy Policy</Link>
                    </p>
                </div>
            </div>

            {/* Simple Email Selection Modal */}
            {showEmailSelection && (
                <div className="fixed inset-0 bg-white bg-opacity-95 flex items-center justify-center z-50 p-4">
                    <div className="bg-white border border-gray-200 rounded-lg p-6 w-full max-w-sm mx-4 shadow-lg">
                        <div className="text-center mb-4">
                            <FcGoogle className="w-8 h-8 mx-auto mb-3" />
                            <h3 className="text-lg font-medium text-gray-900 mb-1">
                                Continue with Google
                            </h3>
                            <p className="text-gray-600 text-sm">
                                Choose an account to continue
                            </p>
                        </div>

                        <div className="space-y-2 mb-4">
                            {availableEmails.length > 0 ? (
                                availableEmails.map((email, index) => (
                                    <button
                                        key={index}
                                        onClick={() => handleEmailSelection(email)}
                                        className="w-full p-3 text-left border border-gray-200 rounded-md hover:bg-gray-50 transition-colors flex items-center"
                                    >
                                        <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                                            {email.charAt(0).toUpperCase()}
                                        </div>
                                        <div className="text-sm text-gray-700">
                                            {email}
                                        </div>
                                    </button>
                                ))
                            ) : (
                                <div className="text-center py-6">
                                    <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-3"></div>
                                    <p className="text-gray-600 text-sm">Loading accounts...</p>
                                </div>
                            )}
                        </div>

                        <div className="border-t pt-4">
                            <button
                                onClick={() => {
                                    setShowEmailSelection(false);
                                    setSocialLoading(prev => ({ ...prev, google: false }));
                                }}
                                className="w-full text-center text-blue-600 text-sm hover:underline"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default SignupPage;