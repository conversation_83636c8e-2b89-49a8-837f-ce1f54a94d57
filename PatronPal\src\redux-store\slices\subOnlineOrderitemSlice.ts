import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import apiClient from "../config"; // Adjust path as needed

// Types based on your schema
export interface DeliveryOptions {
  dropOffAddress?: string;
  dropOffInstructions?: string;
  expressDelivery?: string;
}

export interface PickupOptions {
  standardPickup?: boolean;
  schedulePickup?: boolean;
  startTime?: string;
  endTime?: string;
  Date?: string;
}

export interface PickUpObj {
  pickupAddress?: string;
  pickupOptions?: PickupOptions;
}

export interface Tax {
  name?: string;
  addtax?: number;
}

export interface Customer {
  _id: string;
  name?: string;
  email?: string;
  // Add other customer fields as needed
}

export interface User {
  _id: string;
  name?: string;
  email?: string;
  // Add other user fields as needed
}

export interface OnlineOrderItem {
  restaurant: string;
  _id?: string;
  orderNo?: number; // Make sure this is number, not string
  OrderNumber?: string; // This can remain as string
  Status?: string;
  orderType?: "delivery" | "pickup";
  deliveryOptions?: DeliveryOptions;
  pickUpObj?: PickUpObj;
  orderStatus?: "new order" | "preparing" | "ready" | "done";
  tax?: Tax[];
  product?: any[]; // You can define a more specific type for products
  loyalityOffer?: any[];
  couponOffer?: any[];
  customerId?: string | Customer;
  userId?: string | User;
  PaymentStatus?: "cash" | "online";
  deliveryfee?: number;
  paymentIntentId?: string;
  chargeId?: string;
  Amount?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateOrderRequest {
  Status?: string;
  orderNo?: number; // Add this field as number
  orderType?: "delivery" | "pickup";
  deliveryOptions?: DeliveryOptions;
  pickUpObj?: PickUpObj;
  orderStatus?: "new order" | "preparing" | "ready" | "done";
  tax?: Tax[];
  parentOrderId?: string;
  product: any[];
  loyalityOffer?: any[];
  couponOffer?: any[];
  customerId?: string;
  chargeId?: string;
  paymentIntentId?: string;
  userId: string;
  PaymentStatus?: "cash" | "online";
  deliveryfee?: number;
  Amount?: number;
  OrderNumber?: string; // This remains as string
}

export interface GetOrdersParams {
  userId?: string;
}

export interface OnlineOrderState {
  orders: OnlineOrderItem[];
  kitchenOrders: OnlineOrderItem[];
  lastWeekOrders: OnlineOrderItem[];
  currentOrder: OnlineOrderItem | null;
  loading: boolean;
  error: string | null;
}

const initialState: OnlineOrderState = {
  orders: [],
  kitchenOrders: [],
  lastWeekOrders: [],
  currentOrder: null,
  loading: false,
  error: null,
};

// Async thunks
export const fetchOnlineOrders = createAsyncThunk(
  "onlineOrders/fetchOrders",
  async (
    params: GetOrdersParams | undefined,
    thunkAPI: { rejectWithValue: (value: any) => any }
  ) => {
    try {
      const queryParams = new URLSearchParams();
      if (params?.userId) {
        queryParams.append("userId", params.userId);
      }

      const response = await apiClient.get(
        `/sub-orderitem${
          queryParams.toString() ? `?${queryParams.toString()}` : ""
        }`
      );
      console.log("Fetched orders:", response.data); // Debugging log
      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch orders"
      );
    }
  }
);

export const fetchKitchenOrders = createAsyncThunk(
  "onlineOrders/fetchKitchenOrders",
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await apiClient.get(
        `/sub-orderitem/kitchen?userId=${userId}`
      );
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch kitchen orders"
      );
    }
  }
);

export const fetchLastWeekOrders = createAsyncThunk(
  "onlineOrders/fetchLastWeekOrders",
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await apiClient.get(
        `/sub-orderitem/last-week?userId=${userId}`
      );
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch last week orders"
      );
    }
  }
);

export const fetchOrderById = createAsyncThunk(
  "onlineOrders/fetchOrderById",
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await apiClient.get(`/sub-orderitem/${id}`);
      return response.data[0]; // Controller returns array, so we take first item
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch order"
      );
    }
  }
);

export const createOnlineOrder = createAsyncThunk(
  "onlineOrders/createOrder",
  async (orderData: CreateOrderRequest, { rejectWithValue }) => {
    try {
      const response = await apiClient.post("/sub-orderitem", orderData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to create order"
      );
    }
  }
);

// Slice
const onlineOrderSlice = createSlice({
  name: "onlineOrders",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentOrder: (state) => {
      state.currentOrder = null;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch orders
      .addCase(fetchOnlineOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOnlineOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = action.payload;
      })
      .addCase(fetchOnlineOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch kitchen orders
      .addCase(fetchKitchenOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchKitchenOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.kitchenOrders = action.payload;
      })
      .addCase(fetchKitchenOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch last week orders
      .addCase(fetchLastWeekOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLastWeekOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.lastWeekOrders = action.payload;
      })
      .addCase(fetchLastWeekOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch order by ID
      .addCase(fetchOrderById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrderById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentOrder = action.payload;
      })
      .addCase(fetchOrderById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Create order
      .addCase(createOnlineOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createOnlineOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.orders.unshift(action.payload); // Add new order to beginning of array
        state.currentOrder = action.payload;
      })
      .addCase(createOnlineOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, clearCurrentOrder, setLoading } =
  onlineOrderSlice.actions;

// Selectors
export const selectOrders = (state: { onlineOrders: OnlineOrderState }) =>
  state.onlineOrders.orders;
export const selectKitchenOrders = (state: {
  onlineOrders: OnlineOrderState;
}) => state.onlineOrders.kitchenOrders;
export const selectLastWeekOrders = (state: {
  onlineOrders: OnlineOrderState;
}) => state.onlineOrders.lastWeekOrders;
export const selectCurrentOrder = (state: { onlineOrders: OnlineOrderState }) =>
  state.onlineOrders.currentOrder;
export const selectLoading = (state: { onlineOrders: OnlineOrderState }) =>
  state.onlineOrders.loading;
export const selectError = (state: { onlineOrders: OnlineOrderState }) =>
  state.onlineOrders.error;

export default onlineOrderSlice.reducer;
