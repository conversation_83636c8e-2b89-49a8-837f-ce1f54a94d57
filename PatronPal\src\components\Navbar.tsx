/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useRef, useEffect } from 'react';
import { Heart, ShoppingCart } from 'lucide-react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { selectCurrentCustomer } from '../redux-store/slices/customerSlice';
import { useAppSelector, useAppDispatch } from "../redux-store/hooks";
import {
  selectFormattedAddress,
  selectDeliveryOption,
  type AddressData,
  setFullAddressData,
  setFormattedAddress
} from "../redux-store/slices/addressSlice";
import { selectCartItemCount } from "../redux-store/slices/cartSlice";
import AddressDropdown from "./AddressDropdown";

const Navbar = () => {
  // Add navigate
  const navigate = useNavigate();
  const location = useLocation();

  // Redux state type
  interface RootState {
    customer: any;
    auth?: {
      user?: {
        _id: string;
      };
    };
  }

  const goToFavorites = () => {
    navigate('/favorites'); // Change '/favorites' to your actual route
  };


  const userId = useSelector((state: RootState) => state.customer?.currentCustomer?._id);

  const currentCustomer = useSelector(selectCurrentCustomer);

  // Address state from Redux
  const address = useAppSelector(selectFormattedAddress);
  // const fullAddressData = useAppSelector(selectFullAddressData);
  const selectedOption = useAppSelector(selectDeliveryOption);
  const dispatch = useAppDispatch();

  // Get cart item count from Redux
  const cartItemCount = useAppSelector(selectCartItemCount);

  // Dropdown state
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    console.log(isOpen)
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  },);

  // Handle address selection from dropdown
  const handleSelectAddress = (addressData: AddressData) => {
    dispatch(setFullAddressData(addressData));

    // Format the address for display in the input field and for filtering
    const formattedAddress = [
      addressData.City,
      addressData.State,
      addressData.Country
    ].filter(Boolean).join(', ');

    dispatch(setFormattedAddress(formattedAddress));

    // Save to localStorage for persistence
    localStorage.setItem('lastSelectedAddress', JSON.stringify(addressData));

    // First close the dropdown
    setIsOpen(false);

    // Update URL parameters if on products or restaurant-list page
    if (location.pathname.includes('/all-restaurants') || location.pathname.includes('/restaurant-list')) {
      const searchParams = new URLSearchParams(location.search);
      // Set the address parameter - URLSearchParams handles encoding
      searchParams.set('address', formattedAddress);

      // Keep the existing type parameter or default to delivery
      const typeParam = searchParams.get('type') || selectedOption.toLowerCase();
      searchParams.set('type', typeParam);

      // Navigate to the same page with updated parameters
      navigate(`${location.pathname}?${searchParams.toString()}`, { replace: true });
    } else {
      // If not on products or restaurant-list page, navigate to products page
      // with the selected address and delivery option
      const typeParam = selectedOption.toLowerCase();
      navigate(`/all-restaurants?type=${typeParam}&address=${formattedAddress}`);
    }
  };


  return (
    <header className="w-full bg-white shadow-md md:rounded-none rounded-b-xl">
      <div className=" w-full p-3 md:px-20  flex items-center justify-between">
        {/* User Avatar */}
        {userId && <Link className='block md:hidden' to="/profile-settings">
          <img
            src={currentCustomer?.profile_pic}
            alt="User"
            className="w-9 h-9 rounded-full object-cover border border-gray-300"
          />
        </Link>}
        {/* Logo */}
        <a href="/" className="md:text-xl text-base font-semibold text-black">
          PatronPal
        </a>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center justify-end space-x-6 w-[50%]">
          {/* Location Selector - Updated to match Home.tsx */}
          <div className="relative w-[36%] border rounded-xl border-gray-200" ref={dropdownRef}>
            <AddressDropdown
              value={address}
              onSelectAddress={handleSelectAddress}
              autoFocus={false}
            />
          </div>

          {/* User Avatar */}
          {userId && <Link to="/profile-settings">
            <img
              src={currentCustomer?.profile_pic}
              alt="User"
              className="w-10 h-10 rounded-full object-cover border border-gray-300"
            />
          </Link>}

          {/* Favorites */}
          <div
            className="flex items-center text-sm text-gray-700 cursor-pointer"
            onClick={goToFavorites}
          >
            <Heart className="w-5 h-5 mr-1" />
            Favorites
          </div>

          {/* Cart */}
          <Link to='/cart' className="flex items-center text-sm text-gray-700 cursor-pointer relative">
            <ShoppingCart className="w-5 h-5 mr-1" />
            Cart
            {cartItemCount > 0 && (
              <span className="text-gray-500 text-base flex items-center justify-center ml-1">
                {cartItemCount > 99 ? '99+' : cartItemCount}
              </span>
            )}
          </Link>
        </div>

        {/* Mobile Icons */}
        <div className="flex md:hidden items-center space-x-4">
          {/* Heart Icon */}
          <Heart className="w-6 h-6 text-gray-700" />

          {/* Cart Icon with Badge */}
          <div className="flex justify-end items-center space-x-3 relative">
            <ShoppingCart className="w-6 h-6 text-gray-700" />
            {cartItemCount > 0 && (
              <span className="text-gray-500 text-base flex items-center justify-center ml-1">
                {cartItemCount > 99 ? '99+' : cartItemCount}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Location Bar - Updated to match Home.tsx */}
      <div className="block md:hidden px-3 py-2 pb-3">
        <div className="relative" ref={dropdownRef}>
          <AddressDropdown
            value={address}
            onSelectAddress={handleSelectAddress}
            autoFocus={false}
          />
        </div>
      </div>

    </header>
  );
};

export default Navbar;
