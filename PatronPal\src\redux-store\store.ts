import { configureStore } from "@reduxjs/toolkit";
import {
  type TypedUseSelectorHook,
  useDispatch,
  useSelector,
} from "react-redux";
import { persistStore, persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import { combineReducers } from "@reduxjs/toolkit";

import deviceReducer from "./slices/deviceSlice";
import customerReducer from "./slices/customerSlice";
import productReducer from "./slices/productSlice";
import reviewReducer from "./slices/reviewSlice";
import categoryReducer from "./slices/categorySlice";
import menuReducer from "./slices/menuSlice";
import modifierReducer from "./slices/modifierSlice";
import addressReducer from "./slices/addressSlice";
import parentOrderReducer from "./slices/parentOnlineOrderSlice";
import subOrderReducer from "./slices/subOnlineOrderitemSlice";
import cartReducer from "./slices/cartSlice";

// Configure persist for cart
const cartPersistConfig = {
  key: "cart",
  storage,
  whitelist: ["items", "totalPrice"], // only persist these keys
};

const rootReducer = combineReducers({
  customer: customerReducer,
  product: productReducer,
  device: deviceReducer,
  reviews: reviewReducer,
  category: categoryReducer,
  menu: menuReducer,
  modifier: modifierReducer,
  address: addressReducer,
  parentOnlineOrder: parentOrderReducer,
  onlineOrders: subOrderReducer,
  cart: persistReducer(cartPersistConfig, cartReducer),
});

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
      },
    }),
  devTools: process.env.NODE_ENV !== "production",
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
