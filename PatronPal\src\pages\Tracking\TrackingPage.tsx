import { Link, useSearchParams } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { useEffect, useMemo } from 'react';
import { selectCartItems, selectCartTotal } from '../../redux-store/slices/cartSlice';
import {
    getDeviceById,
    selectDevices,
    selectDeviceLoading,
    selectCurrentDevice
} from '../../redux-store/slices/deviceSlice';
import { assets } from '../../assets/assets';

const TrackingPage = () => {
    const dispatch = useDispatch();
    const [searchParams] = useSearchParams();
    const paymentType = searchParams.get('type') || 'cod';

    // Get cart items and total from Redux store
    const cartItems = useSelector(selectCartItems);
    const cartTotal = useSelector(selectCartTotal);
    const devices = useSelector(selectDevices);
    const deviceLoading = useSelector(selectDeviceLoading);
    const currentDevice = useSelector(selectCurrentDevice);

    // Payment method mapping
    const paymentMethods = {
        cod: "Cash On Delivery",
        card: "Credit Card",
        paypal: "PayPal",
        wallet: "Digital Wallet",
        bank: "Bank Transfer"
    };

    type PaymentMethodKey = keyof typeof paymentMethods;

    // Get payment method text based on type parameter
    const getPaymentMethod = (type: string) => {
        const key = type.toLowerCase() as PaymentMethodKey;
        return paymentMethods[key] || "Cash On Delivery";
    };

    // Group cart items by restaurant
    const groupItemsByRestaurant = (items: any[]) => {
        return items.reduce((groups: { [x: string]: any[]; }, item: { restaurant: { name: string; _id?: string; }; }) => {
            const restaurantName = item.restaurant?.name || "Unknown Restaurant";
            if (!groups[restaurantName]) {
                groups[restaurantName] = [];
            }
            groups[restaurantName].push(item);
            return groups;
        }, {});
    };

    const groupedItems = groupItemsByRestaurant(cartItems);
    const restaurantNames = Object.keys(groupedItems);

    // Get unique restaurant IDs from cart items
    const restaurantIds = useMemo(() => {
        const ids = new Set();
        cartItems.forEach(item => {
            if (item.restaurant?.id || item.restaurantId) {
                ids.add(item.restaurant?.id || item.restaurantId);
            }
        });
        return Array.from(ids) as string[];
    }, [cartItems]);

    // Fetch restaurant/device data for delivery times
    useEffect(() => {
        restaurantIds.forEach(id => {
            if (id) {
                dispatch(getDeviceById(id) as any);
            }
        });
    }, [dispatch, restaurantIds]);

    // ✅ 24-hour → 12-hour formatter
    function formatTime12Hour(timeStr: string): string {
        const [hours, minutes] = timeStr.split(':');
        const date = new Date();
        date.setHours(parseInt(hours));
        date.setMinutes(parseInt(minutes));

        return date.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
        });
    }

    // Calculate estimated delivery time based on restaurant delivery settings
    const calculateDeliveryTime = (restaurant: any) => {
        if (!restaurant) return "30-45 mins";

        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes(); // Current time in minutes

        // Parse delivery times if they exist
        if (restaurant.deliveryStartTime && restaurant.deliveryEndTime) {
            const [startHour, startMin] = restaurant.deliveryStartTime.split(':').map(Number);
            const [endHour, endMin] = restaurant.deliveryEndTime.split(':').map(Number);

            const startTime = startHour * 60 + startMin;
            const endTime = endHour * 60 + endMin;

            // Check if current time is within delivery hours
            if (currentTime >= startTime && currentTime <= endTime) {
                // Base delivery time (30-45 mins) + distance factor if available
                const baseDeliveryTime = 35; // Base 35 minutes
                const distanceFactor = restaurant.ChargesperKm ? Math.min(restaurant.ChargesperKm * 2, 15) : 0;

                const totalMinutes = baseDeliveryTime + distanceFactor;
                const deliveryTime = new Date(now.getTime() + totalMinutes * 60000);

                return `${deliveryTime.getHours().toString().padStart(2, '0')}:${deliveryTime.getMinutes().toString().padStart(2, '0')}`;
            } else {
                // Outside delivery hours - show next available delivery time
                const nextDeliveryTime = new Date();
                nextDeliveryTime.setHours(startHour, startMin + 35, 0, 0);

                if (currentTime > endTime) {
                    // Next day delivery
                    nextDeliveryTime.setDate(nextDeliveryTime.getDate() + 1);
                }

                return `${nextDeliveryTime.getHours().toString().padStart(2, '0')}:${nextDeliveryTime.getMinutes().toString().padStart(2, '0')} (Delivers Tomorrow)`;
            }
        }

        // Default calculation if no specific delivery times
        const deliveryTime = new Date(now.getTime() + 35 * 60000); // 35 minutes from now
        return `${deliveryTime.getHours().toString().padStart(2, '0')}:${deliveryTime.getMinutes().toString().padStart(2, '0')}`;
    };

    // Get restaurant delivery information
    const getRestaurantDeliveryInfo = (restaurantName: string) => {
        const restaurantItems = groupedItems[restaurantName];
        if (!restaurantItems || restaurantItems.length === 0) return null;

        const restaurantId = restaurantItems[0].restaurant?._id || restaurantItems[0].restaurantId;

        // Find restaurant in devices or use currentDevice if it matches
        let restaurant = null;
        if (currentDevice && currentDevice._id === restaurantId) {
            restaurant = currentDevice;
        } else {
            restaurant = devices.find(device => device._id === restaurantId);
        }

        return {
            restaurant,
            estimatedDelivery: calculateDeliveryTime(restaurant),
            isDeliveryAvailable: restaurant?.delivery !== false,
            deliveryCharges: restaurant?.ChargesperKm || 0,
            freeDeliveryKm: restaurant?.ChargesFreeKm || 0
        };
    };

    // Dynamic delivery messages based on payment type and restaurant status
    const getDeliveryMessage = (type: string, restaurantInfo: any) => {
        const isDeliveryAvailable = restaurantInfo?.isDeliveryAvailable !== false;

        if (!isDeliveryAvailable) {
            return {
                line1: "Restaurant is currently not delivering.",
                line2: "You can opt for pickup instead."
            };
        }

        switch (type.toLowerCase()) {
            case 'cod':
                return {
                    line1: "We have found the best rider for you.",
                    line2: "They are heading to the restaurant."
                };
            case 'card':
            case 'paypal':
            case 'wallet':
                return {
                    line1: "Payment confirmed! Your order is being prepared.",
                    line2: "Our rider will pick it up soon."
                };
            case 'bank':
                return {
                    line1: "Payment verified! Order is in preparation.",
                    line2: "Rider assignment in progress."
                };
            default:
                return {
                    line1: "We have found the best rider for you.",
                    line2: "They are heading to the restaurant."
                };
        }
    };

    // Get primary restaurant info (for main display)
    const primaryRestaurantInfo = restaurantNames.length > 0
        ? getRestaurantDeliveryInfo(restaurantNames[0])
        : null;

    // Sample order data with dynamic restaurant information
    const orderDetails = {
        restaurant: restaurantNames.length > 0 ? restaurantNames[0] : "Corny Bar B Q Restaurant",
        paymentMethod: getPaymentMethod(paymentType),
        items: cartItems.length > 0 ? cartItems : [
            { id: 1, name: "No items in cart", price: 0, quantity: 1, image: assets.placeholder, isDropwdown: false }
        ],
        total: cartTotal > 0 ? cartTotal : 0.00,
        estimatedDelivery: primaryRestaurantInfo?.estimatedDelivery || "30-45 mins"
    };

    const deliveryMessage = getDeliveryMessage(paymentType, primaryRestaurantInfo);

    // Show loading state while fetching restaurant data
    if (deviceLoading && restaurantIds.length > 0) {
        return (
            <div className="bg-gray-50 min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
                    <p className="mt-4 text-gray-600">Loading restaurant information...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-gray-50 min-h-screen">
            <div className='max-w-6xl mx-auto pt-4 pb-12'>
                {/* Header */}
                <div className="">
                    <h1 className="text-base font-bold text-gray-800">Tracking</h1>
                </div>

                <div className="py-3 flex flex-col md:flex-row space-x-3">
                    {/* Left Column - Tracking Information */}
                    <div className="md:w-1/1 flex flex-col py-5 h-fit items-center mb-8 md:mb-0 bg-white rounded-2xl">
                        <div className="text-center">
                            <p className="text-gray-400 text-sm mb-2">Estimated Time of Delivery</p>
                            {/* <p className="text-xl font-semibold">{orderDetails.estimatedDelivery}</p> */}
                            {primaryRestaurantInfo?.isDeliveryAvailable === false && (
                                <p className="text-red-500 text-sm mt-1">Delivery currently unavailable</p>
                            )}
                        </div>

                        {/* Multiple Restaurant Delivery Times */}
                        {restaurantNames.length > 0 && (
                            <div className="text-center mb-4 p-3">
                                {restaurantNames.map((restaurantName, index) => {
                                    const info = getRestaurantDeliveryInfo(restaurantName);
                                    return (
                                        <div key={index} className="text-base font-semibold text-gray-600 mb-1">
                                            <span className="font-medium">{restaurantName}:</span> {info?.estimatedDelivery ? formatTime12Hour(info.estimatedDelivery) : "Calculating..."}
                                        </div>
                                    );
                                })}
                            </div>
                        )}

                        {/* Delivery Illustration */}
                        <div className="mb-8">
                            <img
                                src={assets.delivery_tracking}
                                alt="Delivery rider on scooter"
                                className="h-40"
                            />
                        </div>

                        {/* Delivery Status - Dynamic based on payment type and restaurant */}
                        <div className="text-center mb-6">
                            <p className="font-medium">{deliveryMessage.line1}</p>
                            <p className="font-medium">{deliveryMessage.line2}</p>

                            {/* Delivery charges info */}
                            {(primaryRestaurantInfo?.deliveryCharges ?? 0) > 0 && (
                                <div className="mt-3 text-xs text-gray-600">
                                    <p>Delivery: ${primaryRestaurantInfo?.deliveryCharges ?? 0}/km</p>
                                    {(primaryRestaurantInfo?.freeDeliveryKm ?? 0) > 0 && (
                                        <p>Free delivery within {primaryRestaurantInfo?.freeDeliveryKm ?? 0}km</p>
                                    )}
                                </div>
                            )}
                        </div>

                        {/* Track Order Button */}
                        <Link to={'/live-tracking'} className="bg-primary text-white rounded-full py-3 px-6 font-medium">
                            Track Order in Realtime
                        </Link>
                    </div>

                    {/* Right Column - Order Details */}
                    <div className="md:w-1/2 p-6 py-5 bg-white rounded-2xl items-center h-fit">
                        {/* Order Details Section */}
                        <div className="mb-6">
                            <h2 className="text-lg font-bold mb-4">Order Details</h2>
                            <p className="text-gray-600">{orderDetails.paymentMethod}</p>
                        </div>

                        <div className="border-t border-gray-200 pt-6">
                            <h2 className="text-base font-bold mb-4">Your Items</h2>

                            {/* Items List - Grouped by Restaurant with Delivery Times */}
                            <div className="space-y-6 h-[200px] overflow-y-auto">
                                {restaurantNames.length > 0 ? (
                                    restaurantNames.map((restaurantName, restaurantIndex) => {
                                        // const restaurantInfo = getRestaurantDeliveryInfo(restaurantName);

                                        return (
                                            <div key={restaurantIndex}>
                                                {/* Restaurant Header with Delivery Time */}
                                                <div className="mb-3 pb-2 border-b border-gray-100">
                                                    <div className="flex justify-between items-center">
                                                        <h3 className="font-semibold text-gray-800 text-sm">{restaurantName}</h3>
                                                    </div>
                                                </div>

                                                {/* Items from this restaurant */}
                                                <div className="space-y-4">
                                                    {groupedItems[restaurantName].map((item: any, itemIndex: number) => {
                                                        const basePrice = item.discountPrice || item.price;
                                                        const modifierPrice = item.modifierPrice || 0;
                                                        const totalItemPrice = basePrice + modifierPrice;

                                                        return (
                                                            <div key={`${item._id}-${itemIndex}`} className="border-b pb-3 border-gray-200">
                                                                <div className="flex items-center justify-between">
                                                                    <div className="flex items-center w-full">
                                                                        <img
                                                                            src={item.image || assets.placeholder}
                                                                            alt={item.name}
                                                                            className="w-16 h-16 object-cover rounded-lg mr-3"
                                                                        />
                                                                        <div className='w-full space-y-2'>
                                                                            <div className='flex justify-between items-center'>
                                                                                <h3 className="font-medium">{item.name}</h3>
                                                                            </div>

                                                                            <div className='flex justify-between w-full items-end'>
                                                                                <div>
                                                                                    <p className="text-primary font-medium">$ {totalItemPrice.toFixed(2)}</p>
                                                                                    {item.isFree && <p className="text-green-500 text-sm">1x Free</p>}

                                                                                    {/* Modifiers */}
                                                                                    {item.modifiers && Object.keys(item.modifiers).length > 0 && (
                                                                                        <div className="text-sm text-gray-600 flex gap-3">
                                                                                            {Object.values(item.modifiers).map((modifier, modIndex) => {
                                                                                                const mod = modifier as { name: string; price: number };
                                                                                                return (
                                                                                                    <div key={modIndex} className="flex justify-between items-center gap-0.5">
                                                                                                        <span>{mod.name}</span>
                                                                                                        {mod.price > 0 && <span>+${mod.price.toFixed(2)}</span>}
                                                                                                    </div>
                                                                                                );
                                                                                            })}
                                                                                        </div>
                                                                                    )}

                                                                                    {/* Special Note */}
                                                                                    {item.note && (
                                                                                        <div className="text-sm text-gray-500 italic">
                                                                                            Note: {item.note}
                                                                                        </div>
                                                                                    )}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            </div>
                                        );
                                    })
                                ) : (
                                    <div className="text-center text-gray-500 py-8">
                                        <p>No items in cart</p>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Total */}
                        <div className="mt-24 border-dashed border-y border-gray-200 py-5">
                            <div className="flex justify-between items-center mb-2">
                                <h3 className="text-gray-500 text-base font-[400]">Total</h3>
                                <p className="text-primary font-[600] text-base">$ {cartTotal.toFixed(2)}</p>
                            </div>
                            <button className="text-blue-500 font-medium">See Summary</button>
                        </div>

                        {/* Payment Method */}
                        <div className="mt-6 border-gray-200">
                            <div className="flex justify-between items-center">
                                <h3 className="text-gray-500 text-base font-[400]">Payment Method</h3>
                                <p className="text-primary font-[600] text-base">{orderDetails.paymentMethod}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TrackingPage;