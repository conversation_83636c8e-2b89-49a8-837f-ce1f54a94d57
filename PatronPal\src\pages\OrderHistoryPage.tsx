import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { assets } from "../assets/assets";
import { Link } from "react-router-dom";
import { 
    fetchOnlineOrders, 
    selectOrders, 
    selectLoading, 
    selectError,
    type OnlineOrderItem 
} from "../redux-store/slices/subOnlineOrderitemSlice"; // Adjust the import path

interface Order {
    id: string;
    restaurant: string;
    status: "active" | "delivered";
    date: string;
    items: Array<{
        name: string;
        quantity: number;
    }>;
    totalPrice: number;
    orderNumber: string;
}

export default function OrderHistoryPage() {
    const dispatch = useDispatch();
    const reduxOrders = useSelector(selectOrders);
    const loading = useSelector(selectLoading);
    const error = useSelector(selectError);
    
    const [orders, setOrders] = useState<Order[]>([]);

    // Transform Redux orders to match the UI interface
    const transformReduxOrderToUIOrder = (reduxOrder: OnlineOrderItem): Order => {
        // Extract product names and quantities from the product array
        const items = reduxOrder.product?.map(product => ({
            name: product.name || product.title || "Unknown Item",
            quantity: product.quantity || 1
        })) || [{ name: "No items", quantity: 0 }];

        // Determine status based on orderStatus
        const status: "active" | "delivered" = 
            reduxOrder.orderStatus === "done" ? "delivered" : "active";

        // Format date
        const date = reduxOrder.createdAt 
            ? new Date(reduxOrder.createdAt).toLocaleDateString('en-US', {
                weekday: 'short',
                day: 'numeric',
                month: 'short',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            })
            : "Unknown date";

        return {
            id: reduxOrder._id || Math.random().toString(),
            restaurant: "Corny Barb Q Restaurant", // You might want to get this from user data or make it dynamic
            status,
            date,
            items,
            totalPrice: reduxOrder.Amount || 0,
            orderNumber: reduxOrder.OrderNumber || reduxOrder.orderNo?.toString() || "N/A"
        };
    };

    useEffect(() => {
        // Fetch orders from Redux store
        // You might want to pass userId here if needed
        dispatch(fetchOnlineOrders() as any);
    }, [dispatch]);

    useEffect(() => {
        // Transform Redux orders to UI format when reduxOrders changes
        if (reduxOrders && reduxOrders.length > 0) {
            const transformedOrders = reduxOrders.map(transformReduxOrderToUIOrder);
            setOrders(transformedOrders);
        }
    }, [reduxOrders]);

    const activeOrders = orders.filter(order => order.status === "active");
    const pastOrders = orders.filter(order => order.status === "delivered");

    useEffect(() => {
      console.log('activeOrders',activeOrders)
    }, [activeOrders]);

    // Show loading state
      if (loading) {
        return (
            <div className="text-center py-12 h-screen flex flex-col justify-center items-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading orders...</p>
            </div>
        );
    }

    // Show error state
    if (error) {
        return (
            <div className="block min-h-screen bg-gray-50 w-full">
                <div className="max-w-2xl mx-auto p-4 w-full">
                    <div className="flex justify-center items-center h-64">
                        <div className="text-lg text-red-500">Error: {error}</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="block min-h-screen bg-gray-50 w-full">
            <div className="max-w-2xl mx-auto p-4 w-full">
                {/* Active Orders Section */}
                <h2 className="text-base font-bold mb-4">Active Orders</h2>
                <div className="space-y-6 mb-8">
                    {activeOrders.length > 0 ? (
                        activeOrders.map(order => (
                            <OrderCard
                                key={order.id}
                                order={order}
                                link={'/tracking'}
                                buttonText="Track Order in Realtime"
                                buttonColor="bg-primary"
                            />
                        ))
                    ) : (
                        <div className="text-center py-8 text-gray-500">
                            No active orders found
                        </div>
                    )}
                </div>

                {/* Past Orders Section */}
                <h2 className="text-base font-bold mb-4">Past Orders</h2>
                <div className="space-y-6">
                    {pastOrders.length > 0 ? (
                        pastOrders.map(order => (
                            <OrderCard
                                key={order.id}
                                link="#"
                                order={order}
                                buttonText="Reorder"
                                buttonColor="bg-primary"
                            />
                        ))
                    ) : (
                        <div className="text-center py-8 text-gray-500">
                            No past orders found
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

interface OrderCardProps {
    order: Order;
    buttonText: string;
    buttonColor: string;
    link: string;
}

function OrderCard({ order, buttonText, buttonColor, link }: OrderCardProps) {
    return (
        <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-4 p-2.5 border-gray-200 bg-white rounded-2xl">
            <div className="relative w-full sm:w-auto">
                <img
                    src={assets.order_history}
                    alt={order.restaurant}
                    className="w-full sm:w-32 h-32 rounded-md object-cover"
                />
                {order.status === 'delivered' && (
                    <button className="absolute bottom-2 right-2 bg-white rounded-full p-2 shadow-md">
                        <PlusIcon />
                    </button>
                )}
            </div>

            <div className="flex-1 w-full">
                <div className="flex flex-col sm:flex-row justify-between items-start">
                    <div>
                        <h3 className="text-xl font-semibold">{order.restaurant}</h3>
                        <p className="text-gray-500 text-sm">
                            {order.status === "active" ? "Picked up on " : "Delivered on "}
                            {order.date}
                        </p>
                        <p className="text-gray-500 text-sm">Order #{order.orderNumber}</p>
                    </div>
                    <span className="font-bold mt-2 sm:mt-0">$ {order.totalPrice.toFixed(2)}</span>
                </div>

                <div className="mt-2">
                    {order.items.map((item, index) => (
                        <p key={index} className="text-gray-800">
                            {item.quantity}x {item.name}
                        </p>
                    ))}
                </div>

                <div className="mt-4 flex justify-end">
                    <Link to={link} className={`${buttonColor} text-white px-4 py-2 justify-center flex rounded-full hover:opacity-90 w-full sm:w-auto`}>
                        {buttonText}
                    </Link>
                </div>
            </div>
        </div>
    );
}

function PlusIcon() {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-primary"
        >
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
    );
}