/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { ArrowLeft, Mail, RefreshCw } from 'lucide-react';
import type { AppDispatch } from '../../redux-store/store';
import {
    verifyEmailOTP,
    resendEmailOTP,
    selectCustomerLoading,
    selectCustomerError,
    clearError
} from '../../redux-store/slices/customerSlice';

const OtpVerificationPage: React.FC = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const dispatch = useDispatch<AppDispatch>();
    
    const loading = useSelector(selectCustomerLoading);
    const error = useSelector(selectCustomerError);
    
    // Get email from navigation state
    const email = location.state?.email || '';
    
    // OTP state
    const [otp, setOtp] = useState<string[]>(['', '', '', '']);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [resendLoading, setResendLoading] = useState(false);
    const [timer, setTimer] = useState(60);
    const [canResend, setCanResend] = useState(false);
    
    // Refs for OTP inputs
    const otpRefs = useRef<(HTMLInputElement | null)[]>([]);
    
    // Redirect if no email provided
    useEffect(() => {
        if (!email) {
            toast.error('Please complete the signup process first');
            navigate('/signup');
        }
    }, [email, navigate]);
    
    // Clear error on component mount
    useEffect(() => {
        dispatch(clearError());
    }, [dispatch]);
    
    // Timer countdown
    useEffect(() => {
        let interval: NodeJS.Timeout;
        
        if (timer > 0 && !canResend) {
            interval = setInterval(() => {
                setTimer((prev) => prev - 1);
            }, 1000);
        } else if (timer === 0) {
            setCanResend(true);
        }
        
        return () => clearInterval(interval);
    }, [timer, canResend]);
    
    // Handle OTP input change
    const handleOtpChange = useCallback((index: number, value: string) => {
        // Only allow digits
        if (!/^\d*$/.test(value)) return;
        
        const newOtp = [...otp];
        newOtp[index] = value;
        setOtp(newOtp);
        
        // Auto-focus next input
        if (value && index < 3) {
            otpRefs.current[index + 1]?.focus();
        }
    }, [otp]);
    
    // Handle backspace
    const handleKeyDown = useCallback((index: number, e: React.KeyboardEvent) => {
        if (e.key === 'Backspace' && !otp[index] && index > 0) {
            otpRefs.current[index - 1]?.focus();
        }
    }, [otp]);
    
    // Handle paste
    const handlePaste = useCallback((e: React.ClipboardEvent) => {
        e.preventDefault();
        const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, 4);
        
        if (pastedData.length === 4) {
            const newOtp = pastedData.split('');
            setOtp(newOtp);
            otpRefs.current[3]?.focus();
        }
    }, []);
    
    // Verify OTP
    const handleVerifyOtp = useCallback(async () => {
        const otpString = otp.join('');
        
        if (otpString.length !== 4) {
            toast.error('Please enter a complete 4-digit OTP');
            return;
        }
        
        setIsSubmitting(true);
        
        try {
            const result = await dispatch(verifyEmailOTP({
                email: email.toLowerCase(),
                otp: otpString
            }));
            
            if (verifyEmailOTP.fulfilled.match(result)) {
                toast.success('Email verified successfully! Redirecting to login...');
                setTimeout(() => {
                    navigate('/login', { 
                        state: { 
                            email,
                            message: 'Account verified successfully! Please login to continue.' 
                        }
                    });
                }, 1500);
            } else if (verifyEmailOTP.rejected.match(result)) {
                const errorMessage = result.payload as string;
                
                if (errorMessage?.toLowerCase().includes('invalid') || 
                    errorMessage?.toLowerCase().includes('incorrect')) {
                    toast.error('Invalid OTP. Please check and try again.');
                } else if (errorMessage?.toLowerCase().includes('expired')) {
                    toast.error('OTP has expired. Please request a new one.');
                    setCanResend(true);
                    setTimer(0);
                } else {
                    toast.error(errorMessage || 'OTP verification failed. Please try again.');
                }
                
                // Clear OTP on error
                setOtp(['', '', '', '']);
                otpRefs.current[0]?.focus();
            }
        } catch (error) {
            console.error('OTP verification error:', error);
            toast.error('An unexpected error occurred. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    }, [otp, email, dispatch, navigate]);
    
    // Resend OTP
    const handleResendOtp = useCallback(async () => {
        if (!canResend) return;
        
        setResendLoading(true);
        
        try {
            const result = await dispatch(resendEmailOTP({ email: email.toLowerCase() }));
            
            if (resendEmailOTP.fulfilled.match(result)) {
                toast.success('New OTP sent to your email!');
                setTimer(60);
                setCanResend(false);
                setOtp(['', '', '', '']);
                otpRefs.current[0]?.focus();
            } else if (resendEmailOTP.rejected.match(result)) {
                const errorMessage = result.payload as string;
                toast.error(errorMessage || 'Failed to resend OTP. Please try again.');
            }
        } catch (error) {
            console.error('Resend OTP error:', error);
            toast.error('Failed to resend OTP. Please try again.');
        } finally {
            setResendLoading(false);
        }
    }, [canResend, email, dispatch]);
    
    // Format timer display
    const formatTimer = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };
    
    if (!email) {
        return null; // Will redirect in useEffect
    }
    
    return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
            <ToastContainer />
            <div className="w-full max-w-md">
                {/* Header */}
                <div className="text-center mb-8">
                    <div className="flex items-center justify-center mb-6">
                        <Link 
                            to="/signup" 
                            className="absolute left-4 p-2 text-gray-600 hover:text-gray-800 transition-colors"
                        >
                            <ArrowLeft size={24} />
                        </Link>
                        <div className="bg-primary/10 p-3 rounded-full">
                            <Mail className="text-primary" size={32} />
                        </div>
                    </div>
                    
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">
                        Verify Your Email
                    </h1>
                    
                    <p className="text-gray-600 text-sm">
                        We've sent a 4-digit verification code to
                    </p>
                    <p className="text-primary font-medium text-sm">
                        {email}
                    </p>
                </div>
                
                {/* OTP Input */}
                <div className="mb-6">
                    <div className="flex justify-center space-x-3 mb-4">
                        {otp.map((digit, index) => (
                            <input
                                key={index}
                                ref={(el) => (otpRefs.current[index] = el)}
                                type="text"
                                inputMode="numeric"
                                maxLength={1}
                                value={digit}
                                onChange={(e) => handleOtpChange(index, e.target.value)}
                                onKeyDown={(e) => handleKeyDown(index, e)}
                                onPaste={handlePaste}
                                className="w-14 h-14 text-center text-xl font-bold border-2 border-gray-300 rounded-xl focus:border-primary focus:outline-none transition-colors"
                                disabled={isSubmitting}
                            />
                        ))}
                    </div>
                    
                    {error && (
                        <p className="text-red-500 text-sm text-center mb-4">
                            {error}
                        </p>
                    )}
                </div>
                
                {/* Verify Button */}
                <button
                    onClick={handleVerifyOtp}
                    disabled={isSubmitting || otp.join('').length !== 4}
                    className="w-full bg-primary text-white py-3 rounded-xl font-medium hover:bg-primary/90 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors mb-6"
                >
                    {isSubmitting ? 'Verifying...' : 'Verify Email'}
                </button>
                
                {/* Resend Section */}
                <div className="text-center">
                    <p className="text-gray-600 text-sm mb-2">
                        Didn't receive the code?
                    </p>
                    
                    {canResend ? (
                        <button
                            onClick={handleResendOtp}
                            disabled={resendLoading}
                            className="text-primary font-medium text-sm hover:underline disabled:opacity-50 flex items-center justify-center space-x-1"
                        >
                            {resendLoading && <RefreshCw size={16} className="animate-spin" />}
                            <span>{resendLoading ? 'Sending...' : 'Resend Code'}</span>
                        </button>
                    ) : (
                        <p className="text-gray-500 text-sm">
                            Resend code in {formatTimer(timer)}
                        </p>
                    )}
                </div>
                
                {/* Back to Signup */}
                <div className="text-center mt-6">
                    <Link 
                        to="/signup" 
                        className="text-gray-600 text-sm hover:text-gray-800 transition-colors"
                    >
                        Back to Sign Up
                    </Link>
                </div>
            </div>
        </div>
    );
};

export default OtpVerificationPage;
