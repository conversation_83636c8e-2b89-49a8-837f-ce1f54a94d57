import mongoose from 'mongoose';
var current = new Date();
const timeStamp = new Date(Date.UTC(current.getFullYear(),
    current.getMonth(), current.getDate(), current.getHours(),
    current.getMinutes(), current.getSeconds(), current.getMilliseconds()));

const addressSchema = new mongoose.Schema({
    street: {
        type: String,
        required: true
    },
    city: {
        type: String,
        required: true
    },
    state: {
        type: String,
        required: true
    },
    zipcode: {
        type: String,
        required: true
    }
});

const cardSchema = new mongoose.Schema({
    cardholdername: {
        type: String,
        required: true
    },
    cardholdernumber: {
        type: String,
        required: true
    },
    expiry: {
        type: String,
        required: true
    },
    cvv: {
        type: String,
        required: true
    },
    phonenumber: {
        type: String,
        required: true
    },
    email: {
        type: String,
        required: true
    },
    billingaddress: {
        type: String,
        required: true
    },
    state: {
        type: String,
        required: true
    },
    city: {
        type: String,
        required: true
    },
    zipcode: {
        type: String,
        required: true
    },
    dateofbirth: {
        type: String,
        required: true
    }
});

const patronPalCustomerSchema = new mongoose.Schema({
    CustomerId: {
        type: String
    },
    userId: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'user'
    }],
    FirstName: {
        type: String
    },
    LastName: {
        type: String
    },
    googleId: {
        type: String,
    },
    profile_pic: {
        type: String
    },
    Phone: {
        type: String
    },
    Address1: addressSchema,
    Address2: addressSchema,
    birthDate: {
        type: String
    },
    Email: {
        type: String,
        unique: true
    },
    Membership: {
        type: String
    },
    Password: {
        type: String
    },
    ConfirmPassword: {
        type: String
    },
    isActive: {
        type: Boolean
    },
    CustomerLoyalty: {
        CardNo: {
            type: String
        },
        Type: {
            type: String,
            enum: ["client", "employee"]
        },
        StartDate: {
            type: String
        },
        ExpiresIn: {
            type: String
        },
        creditLimits: {
            type: Number
        },
        ActivateCard: {
            type: Boolean
        },
        Points: {
            type: Number
        },
        Visits: {
            type: Number
        },
        LastVisit: {
            type: Date,
            default: timeStamp
        },
        Purchases: {
            type: Number
        },
        SpentAmount: {
            type: Number
        },
        Gender: {
            type: String
        },
        Notes: {
            type: [String]
        }
    },
    resetPasswordOTP: {
        type: Number
    },
    verify: {
        type: Boolean,
        default: false
    },
    cardDetails: cardSchema, // New field for card details
    CustomerReferral: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'CustomerReferral'
    }],
    usersreferrals: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'UserReferral'
    }]
});

const PatronPalCustomer = mongoose.model('PatronPalCustomer', patronPalCustomerSchema);
export default PatronPalCustomer;
