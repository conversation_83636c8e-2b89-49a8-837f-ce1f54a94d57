import PatronPalCustomer from '../models/patronpalCustomers.js';  // Adjust the import path as needed
import jwt from 'jsonwebtoken';
import sendMail from '../middlewares/send-email.js';
import { CustomerReferral } from '../models/referral.js';



export const customerRegister = async (req, res) => {
    let { Email, Password, ConfirmPassword, referralCode } = req.body;

    try {
        Email = Email.toLowerCase();

        // Check if a user with the same email already exists
        const existingUser = await PatronPalCustomer.findOne({ Email });
        if (existingUser) {
            // If user exists but is not verified, allow re-registration with new OTP
            if (!existingUser.verify) {
                // Generate new OTP
                const otp = Math.floor(1000 + Math.random() * 9000);
                existingUser.resetPasswordOTP = otp;
                existingUser.otpCreatedAt = new Date();

                // Update password if provided
                if (Password) {
                    existingUser.Password = Password;
                    existingUser.ConfirmPassword = ConfirmPassword;
                }

                // Send confirmation email with new OTP
                const subject = "Email Confirmation - PatronPal";
                const message = `
                    <h2>Welcome back to PatronPal!</h2>
                    <p>Your new account verification code is: <strong>${otp}</strong></p>
                    <p>Please enter this code in the app to confirm your email address.</p>
                `;

                await sendMail(Email, subject, message);
                await existingUser.save();

                return res.status(200).json({
                    message: "Verification email resent. Please verify your email.",
                    user: existingUser
                });
            } else {
                // User exists and is already verified
                return res.status(400).json({ message: "Email address is already registered" });
            }
        }

        // If a referral code is provided, check its validity and usage
        if (referralCode) {
            const referral = await CustomerReferral.findOne({ referralCode: referralCode });
            if (!referral) {
                return res.status(404).json({ message: "Referral code not found." });
            }
            if (referral.points) {
                return res.status(400).json({ message: "Referral code has already been used." });
            }
        }

        // Create new customer
        const newCustomer = new PatronPalCustomer({ Email, Password, ConfirmPassword });
        newCustomer.verify = false;

        // Generate OTP
        const otp = Math.floor(1000 + Math.random() * 9000);
        newCustomer.resetPasswordOTP = otp;
        newCustomer.otpCreatedAt = new Date();

        // Send confirmation email with OTP
        const subject = "Email Confirmation - PatronPal";
        const message = `
            <h2>Welcome to PatronPal!</h2>
            <p>Your account verification code is: <strong>${otp}</strong></p>
            <p>Please enter this code in the app to confirm your email address.</p>
        `;

        await sendMail(Email, subject, message);

        // Save new customer
        const savedCustomer = await newCustomer.save();

        // Update referral if code was provided
        if (savedCustomer && referralCode) {
            const referral = await CustomerReferral.findOne({ referralCode: referralCode });
            if (referral) {
                referral.referredCustomer = savedCustomer._id;
                await referral.save();
            }
        }

        res.status(200).json({ message: "New customer registered successfully. Please verify your email.", user: savedCustomer });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: "An error occurred", error: error.message });
    }
};

export const updateCustomer = async (req, res) => {
    const userId = req.params.userId;
    const { Address1, Address2, contact, dateOfBirth, fname, lname, cardDetails, profile_pic } = req.body;

    try {
        const user = await PatronPalCustomer.findById(userId);
        
        if (!user) {
            return res.status(404).send({ message: "User not found" });
        }

        if (Address1) {
            user.Address1 = {
                street: Address1.street || user.Address1.street,
                city: Address1.city || user.Address1.city,
                state: Address1.state || user.Address1.state,
                zipcode: Address1.zipcode || user.Address1.zipcode
            };
            
            const referral = await CustomerReferral.findOne({ referredCustomer: userId });
            if (referral) {
                referral.points = true;
                await referral.save();
            }

            user.verify = true;

        }

        if (Address2) {
            user.Address2 = {
                street: Address2.street || user.Address2.street,
                city: Address2.city || user.Address2.city,
                state: Address2.state || user.Address2.state,
                zipcode: Address2.zipcode || user.Address2.zipcode
            };
        }

        if (contact) user.Phone = contact;
        if (profile_pic) user.profile_pic = profile_pic;
        if (dateOfBirth) user.birthDate = dateOfBirth;
        if (fname) user.FirstName = fname;
        if (lname) user.LastName = lname;

        if (cardDetails) {
            user.cardDetails = {
                cardholdername: cardDetails.cardholdername || user.cardDetails.cardholdername,
                cardholdernumber: cardDetails.cardholdernumber || user.cardDetails.cardholdernumber,
                expiry: cardDetails.expiry || user.cardDetails.expiry,
                cvv: cardDetails.cvv || user.cardDetails.cvv,
                phonenumber: cardDetails.phonenumber || user.cardDetails.phonenumber,
                email: cardDetails.email || user.cardDetails.email,
                billingaddress: cardDetails.billingaddress || user.cardDetails.billingaddress,
                state: cardDetails.state || user.cardDetails.state,
                city: cardDetails.city || user.cardDetails.city,
                zipcode: cardDetails.zipcode || user.cardDetails.zipcode,
                dateofbirth: cardDetails.dateofbirth || user.cardDetails.dateofbirth
            };
        }
         // Always set verify field to true

        const updatedUser = await user.save();

        if (updatedUser) {
            const token = jwt.sign({ _id: updatedUser._id }, process.env.JWT_SECRET);
            const UserName = updatedUser.FirstName + ' ' + updatedUser.LastName;
            const { _id, profile_pic, FirstName, LastName, Email, verify } = updatedUser;

            return res.status(200).send({
                success: true,
                message: "Customer profile updated successfully",
                token,
                UserName,
                _id,
                profile_pic,
                FirstName,
                LastName,
                email:Email,
                verify,
                user: updatedUser
            });
        } else {
            return res.status(400).send({ message: "Failed to update user profile" });
        }
    } catch (error) {
        console.error(error);
        return res.status(500).send({ message: "An error occurred", error: error.message });
    }
};

export const customerLogin = async (req, res) => {
    let { Email, Password } = req.body;

    try {
        Email = Email.toLowerCase();

        const user = await PatronPalCustomer.findOne({ Email });
        if (!user) {
            return res.status(400).send({ message: "User not found" });
        }
        if (user.Password !== Password) {
            return res.status(400).send({ message: "Wrong password" });
        }

        // Check if user email is verified
        if (!user.verify) {
            return res.status(403).send({
                message: "Please verify your email address before logging in",
                success: false,
                needsVerification: true,
                email: Email
            });
        }

        const token = jwt.sign({ _id: user._id }, process.env.JWT_SECRET);
        const UserName = user.FirstName + ' ' + user.LastName;
        const { _id, profile_pic, FirstName, LastName, Email: email , verify:verify } = user;
        res.status(200).send({ success: true,message: "Customer login successfully", token, UserName, _id, profile_pic, FirstName, LastName, email, verify , user });
    } catch (error) {
        res.status(500).send({ message: "An error occurred", error: error.message });
        console.log(error);
    }
};

export const sendUserPasswordResetOTP = async (req, res) => {
    try {
        const { email } = req.body;
        if (!email) {
            return res.status(400).send({ message: 'Please provide an email' });
        }
        const user = await PatronPalCustomer.findOne({ Email: email });
        if (!user) {
            return res.status(404).send({ message: 'Email does not exist' });
        }

        // Generate OTP
        const otp = Math.floor(1000 + Math.random() * 9000);

        // Save OTP to user document with timestamp
        user.resetPasswordOTP = otp;
        user.otpCreatedAt = new Date();
        await user.save();

        // Send OTP to user's email
        await sendMail(email, "Forgot Password", `<h2>Hi ${user.FirstName} ${user.LastName}, Your OTP for password reset is: ${otp}</h2>`);

        res.status(200).send({ message: "Password Reset OTP Sent. Please Check Your Email", success: true, });
    } catch (error) {
        res.status(500).send({ message: 'Internal server error', error: error.message });
        console.log('Error in sendUserPasswordResetOTP API', error);
    }
};

export const confirmEmailVerification = async (req, res) => {
    try {
        const { email, otp } = req.body;
        if (!email || !otp) {
            return res.status(400).send({ message: 'Please provide both email and OTP' });
        }

        const user = await PatronPalCustomer.findOne({ Email: email.toLowerCase() });
        if (!user) {
            return res.status(404).send({ message: 'Email does not exist' });
        }

        if (user.resetPasswordOTP !== parseInt(otp)) {
            return res.status(400).send({ message: 'Invalid OTP' });
        }

        // Check OTP expiration (1 minute = 60 seconds)
        if (user.otpCreatedAt) {
            const now = new Date();
            const otpAge = (now.getTime() - user.otpCreatedAt.getTime()) / 1000; // in seconds

            if (otpAge > 60) { // 1 minute expiration
                // Clear expired OTP
                user.resetPasswordOTP = null;
                user.otpCreatedAt = null;
                await user.save();
                return res.status(400).send({ message: 'OTP has expired. Please request a new one.' });
            }
        }

        // Set user as verified and clear OTP
        user.verify = true;
        user.resetPasswordOTP = null;
        user.otpCreatedAt = null;
        await user.save();

        // Generate JWT token for the verified user
        const token = jwt.sign({ _id: user._id }, process.env.JWT_SECRET);
        const UserName = user.FirstName + ' ' + user.LastName;
        const { _id, profile_pic, FirstName, LastName, Email: userEmail, verify } = user;

        res.status(200).send({
            message: "Email verified successfully",
            success: true,
            token,
            UserName,
            _id,
            profile_pic,
            FirstName,
            LastName,
            email: userEmail,
            verify,
            user
        });
    } catch (error) {
        res.status(500).send({ message: 'Internal server error', error: error.message });
        console.log('Error in confirmEmailVerification API', error);
    }
};

export const confirmUserPasswordResetOTP = async (req, res) => {
    try {
        const { email, otp } = req.body;
        if (!email || !otp) {
            return res.status(400).send({ message: 'Please provide both email and OTP' });
        }
        const user = await PatronPalCustomer.findOne({ Email: email });
        if (!user) {
            return res.status(404).send({ message: 'Email does not exist' });
        }
        if (user.resetPasswordOTP !== parseInt(otp)) {
            return res.status(400).send({ message: 'Invalid OTP' });
        }

        // Check OTP expiration (1 minute = 60 seconds)
        if (user.otpCreatedAt) {
            const now = new Date();
            const otpAge = (now.getTime() - user.otpCreatedAt.getTime()) / 1000; // in seconds

            if (otpAge > 60) { // 1 minute expiration
                // Clear expired OTP
                user.resetPasswordOTP = null;
                user.otpCreatedAt = null;
                await user.save();
                return res.status(400).send({ message: 'OTP has expired. Please request a new one.' });
            }
        }

        user.resetPasswordOTP = null;
        user.otpCreatedAt = null;
        await user.save();
        res.status(200).send({ message: "OTP Confirmed Successfully" ,success: true, });
    } catch (error) {
        res.status(500).send({ message: 'Internal server error', error: error.message });
        console.log('Error in confirmUserPasswordResetOTP API', error);
    }
};

export const updateUserPassword = async (req, res) => {
    try {
        const { email, newPassword } = req.body;
        if (!email || !newPassword) {
            return res.status(400).send({ message: 'Please provide email and new password' });
        }
        const user = await PatronPalCustomer.findOne({ Email: email });
        if (!user) {
            return res.status(404).send({ message: 'Email does not exist' });
        }
        user.Password = newPassword;
        await user.save();
        res.status(200).send({ message: "Password updated successfully",success: true, });
    } catch (error) {
        res.status(500).send({ message: 'Internal server error', error: error.message });
        console.log('Error in updateUserPassword API', error);
    }
};


// export const registerByFacebook = async (req, res) => {
//     try {
//         const { FirstName, LastName, Email, profile_pic, googleId, referralCode } = req.body;

//         // Log received data for debugging
//         console.log('Request Body:', req.body);

//         // Check if the user already exists by email
//         let existingUser = await PatronPalCustomer.findOne({ Email });
//         console.log('Existing user:', existingUser);

//         if (existingUser) {
//             console.log("User already exists");

//             // // Check if the user is registered with another method (e.g., email/password)
//             // if (existingUser.Password) {
//             //     return res.status(400).json({
//             //         success: false,
//             //         message: "Email is already registered with another method.",
//             //     });
//             // }

//             // Update the user with Facebook information if not already present
//             if (!existingUser.googleId) {
//                 existingUser.googleId = googleId;
//                 existingUser.profile_pic = profile_pic;
//                 await existingUser.save();
//             }

//             // Load the secret key from environment variables or use a default
//             const secretKey = process.env.JWT_SECRET_KEY || 'defaultSecretKey';

//             // Generate a JWT token for the existing user
//             const token = jwt.sign({ userId: existingUser._id }, secretKey, { expiresIn: '1h' });
//             const UserName = `${existingUser.FirstName} ${existingUser.LastName}`;
//             const { _id, profile_pic: existingProfilePic, FirstName: existingFirstName, LastName: existingLastName, verify } = existingUser;

//             // Respond with success and user information
//             return res.status(200).json({
//                 success: true,
//                 message: "User signed in successfully",
//                 token,
//                 UserName,
//                 _id,
//                 profile_pic: existingProfilePic,
//                 FirstName: existingFirstName,
//                 LastName: existingLastName,
//                 email: Email,
//                 verify,
//                 user: existingUser
//             });
//         } else {
//             console.log("User not found, registering new user");

//             // If a referral code is provided
//             if (referralCode) {
//                 // Find the referral entry with the provided referral code
//                 const referral = await CustomerReferral.findOne({ referralCode: referralCode });

//                 if (referral) {
//                     if (referral.points) {
//                         // Referral code has already been used
//                         return res.status(400).json({ message: "Referral code has already been used." });
//                     }

//                     // Update the referredCustomer with the new customer's ID
//                     referral.referredCustomer = result._id;
//                     // // Set the points to true
//                     // referral.points = true;
//                     await referral.save();
//                 } else {
//                     // Referral code not found
//                     return res.status(404).json({ message: "Referral code not found." });
//                 }
//             }

//             // Save new user data to the database
//             console.log('Customer Data to Save:', { FirstName, LastName, Email, profile_pic, googleId });
//             const customerData = new PatronPalCustomer({ FirstName, LastName, Email, profile_pic, googleId });

//             const result = await customerData.save();

//             // Generate a JWT token for the newly registered user
//             const secretKey = process.env.JWT_SECRET_KEY || 'defaultSecretKey';
//             const token = jwt.sign({ userId: result._id }, secretKey, { expiresIn: '1h' });
//             const UserName = `${result.FirstName} ${result.LastName}`;

//             // Respond with success and user information
//             return res.status(200).json({
//                 success: true,
//                 message: "User registered successfully",
//                 token,
//                 UserName,
//                 _id: result._id,
//                 profile_pic: result.profile_pic,
//                 FirstName: result.FirstName,
//                 LastName: result.LastName,
//                 email: Email,
//                 verify: result.verify,
//                 user: result
//             });
//         }
//     } catch (err) {
//         console.error(err);
//         return res.status(500).json({ message: "An error occurred", error: err.message });
//     }
// };

export const signupByGoogle = async (req, res) => {
    try {
        const { FirstName, LastName, Email, profile_pic, googleId, referralCode } = req.body;

        // console.log("googleId", googleId);

        // Check if the user already exists by email or googleId
        let existingUser = await PatronPalCustomer.findOne({ $or: [{ Email }, { googleId }] });

        if (existingUser) {
            // User already exists, return an error
            return res.status(400).json({
                success: false,
                message: "Email is already registered. Please log in instead.",
            });
        }

        // Handle referral code if provided
        if (referralCode) {
            const referral = await CustomerReferral.findOne({ referralCode: referralCode });

            if (referral) {
                if (referral.points) {
                    return res.status(400).json({ message: "Referral code has already been used." });
                }

                referral.referredCustomer = result._id;
                await referral.save();
            } else {
                return res.status(404).json({ message: "Referral code not found." });
            }
        }

        // Save the new user data to the database
        const customerData = new PatronPalCustomer({ FirstName, LastName, Email, profile_pic, googleId });
        const result = await customerData.save();

        // Generate a JWT token for the newly registered user
        const secretKey = process.env.JWT_SECRET_KEY || 'defaultSecretKey';
        const token = jwt.sign({ userId: result._id }, secretKey, { expiresIn: '1h' });
        const UserName = `${result.FirstName} ${result.LastName}`;

        return res.status(200).json({
            success: true,
            message: "User registered successfully",
            token,
            UserName,
            _id: result._id,
            profile_pic: result.profile_pic,
            FirstName: result.FirstName,
            LastName: result.LastName,
            email: Email,
            verify: result.verify,
            user: result
        });
    } catch (err) {
        console.error(err);
        return res.status(500).json({ message: "An error occurred", error: err.message });
    }
};


export const loginByGoogle = async (req, res) => {
    try {
        const { Email, googleId } = req.body;

        // Check if the user exists by email
        let existingUser = await PatronPalCustomer.findOne({ Email });

        if (!existingUser) {
            // User does not exist, return an error
            return res.status(404).json({
                success: false,
                message: "User not found. Please sign up first.",
            });
        }

        // User exists, generate a JWT token
        const secretKey = process.env.JWT_SECRET_KEY || 'defaultSecretKey';
        const token = jwt.sign({ userId: existingUser._id }, secretKey, { expiresIn: '1h' });
        const UserName = `${existingUser.FirstName} ${existingUser.LastName}`;

        return res.status(200).json({
            success: true,
            message: "User logged in successfully",
            token,
            UserName,
            _id: existingUser._id,
            profile_pic: existingUser.profile_pic,
            FirstName: existingUser.FirstName,
            LastName: existingUser.LastName,
            email: existingUser.Email,
            verify: existingUser.verify,
            user: existingUser
        });
    } catch (err) {
        console.error(err);
        return res.status(500).json({ message: "An error occurred", error: err.message });
    }
};


// Add User to Customer
export const addUserToCustomer = async (req, res) => {
    const { customerId, newUserId } = req.body;

    try {
        const customer = await PatronPalCustomer.findById(customerId);
        if (!customer) {
            return res.status(404).send({ message: "Customer not found" });
        }

        // Push newUserId to userId array
        customer.userId.push(newUserId);
        await customer.save();

        return res.status(200).send({ message: "User added to customer successfully", customer, success: true, });
    } catch (error) {
        console.log(error);
        return res.status(500).send({ message: "Internal server error", error: error.message });
    }
};
